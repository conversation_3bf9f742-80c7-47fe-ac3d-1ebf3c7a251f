:root {
    --primary-text: #eef0f4;
    --secondary-text: #8a8c90;
    --accent-color: #ffac2d;
    --background-dark: #1f2022;
    --banned-color: red;
    --transition-speed: 0.3s;
    --glow-color: rgba(255, 172, 45, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: var(--background-dark);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
}

/* Layout */
.app-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
}

.movie-year-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 100px 20px 0;
    animation: fadeInUp 0.6s ease-out;
}

.movie-list-container {
    margin-bottom: -0.18rem;
}

.movie-list {
    list-style: none;
}

/* Movie Item */
.movie-item {
    display: inline-block;
    margin: 10px;
    vertical-align: top;
}

/* Movie Card Effects */
.movie-card {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    color: var(--primary-text);
    position: relative;
    transform: perspective(1000px);
    transition: transform var(--transition-speed) ease, 
              box-shadow var(--transition-speed) ease;
}

.movie-card:hover {
    transform: translateY(-5px) perspective(1000px) rotateX(2deg);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Image Container Effects */
.movie-image-container {
    width: 88px;
    height: 123px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    margin: 0 auto;
    background: linear-gradient(45deg, #1a1b1d, #2a2b2d);
}

.movie-poster {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform var(--transition-speed) ease;
}

.movie-card:hover .movie-poster {
    transform: scale(1.05);
}

/* Glow Effect */
.glow-overlay {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, var(--glow-color) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    pointer-events: none;
}

.movie-card:hover .glow-overlay {
    opacity: 1;
}

/* Movie Info */
.movie-info {
    width: 88px;
    margin-top: 4px;
    font-size: 13px;
    font-weight: 500;
    color: var(--primary-text);
    word-break: break-word;
}

/* Title Animation */
.movie-title {
    position: relative;
    overflow: hidden;
}

.movie-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-color);
    transform: translateX(-100%);
    transition: transform var(--transition-speed) ease;
}

.movie-card:hover .movie-title::after {
    transform: translateX(0);
}

/* Rating Pop Effect */
.rating-pop {
    display: inline-block;
    color: var(--accent-color);
    font-weight: 500;
    transition: transform var(--transition-speed) ease;
}

.movie-card:hover .rating-pop {
    transform: scale(1.1) translateY(-2px);
}

/* Done Slide Effect */
.done-slide {
    font-size: 12px;
    color: var(--secondary-text);
    font-weight: normal;
    /* transform: translateX(-10px); */
    /* opacity: 0; */
    opacity: .8;
    transition: transform var(--transition-speed) ease, 
              opacity var(--transition-speed) ease;
}

.movie-card:hover .done-slide {
    transform: translateX(0);
    opacity: 1;
}

/* Badge */
.movie-badge {
    position: absolute;
    top: 0;
    left: 0;
    width: 46px;
    height: 22px;
    font-size: 13px;
    line-height: 26px;
    text-align: center;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjZweCIgaGVpZ2h0PSIyMnB4IiB2aWV3Qm94PSIwIDAgMjYgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogICAgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGcgaWQ9Iumhtemdoi0yIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iLSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTI2NC4wMDAwMDAsIC0zNzMuMDAwMDAwKSI+CiAgICAgICAgICAgIDxnIGlkPSIxMTYiPgogICAgICAgICAgICAgICAgPHVzZSBmaWxsPSIjRkZGRkZGIiB4bGluazpocmVmPSIjcGF0aC0yIj48L3VzZT4KICAgICAgICAgICAgICAgIDx1c2UgZmlsbD0idXJsKCNsaW5lYXJHcmFkaWVudC0xKSIgeGxpbms6aHJlZj0iI3BhdGgtMiI+PC91c2U+CiAgICAgICAgICAgIDwvZz4KICAgICAgICAgICAgPGcgaWQ9Iue8lue7hC00IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgyNjQuMDAwMDAwLCAzNzMuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cGF0aAogICAgICAgICAgICAgICAgICAgIGQ9Ik02LDAgTDI2LDAgTDI2LDAgTDIwLjA1MDM0NzgsMTQuNTQzNTk0MSBDMTguMjA1Mzg4NSwxOS4wNTM0OTQ4IDEzLj81NjQ3MTQsMjIgOC45NDM3ODQzNywyMiBMMCwyMiBMMCwyMiBMMCw2IEMtNC4wNTgxMjI1MWUtMTZsMi42ODYyOTE1IDIuNjg2MjkxNSw2LjA4NzE4Mzc2ZS0xNiA2LDAgWiIKICAgICAgICAgICAgICAgICAgICBpZD0i55+p5b2iIiBmaWxsLW9wYWNpdHk9IjAuOSIgZmlsbD0iIzQwNTU4MCI+PC9wYXRoPgogICAgICAgICAgICA8L2c+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4K) no-repeat center/cover;
    transition: transform var(--transition-speed) ease;
}

.movie-card:hover .movie-badge {
    transform: scale(1.1);
}

/* Typography */
.title {
    color: var(--primary-text);
    font-size: 20px;
    margin: 0 0 1rem 1.4rem;
}

.count {
    font-size: 12px;
    color: var(--secondary-text);
    font-weight: normal;
    opacity: 0.2;
}

.banned {
    color: var(--banned-color);
}

/* Background Elements */
.background-container {
    position: absolute;
    inset: 0;
    background: no-repeat center/cover;
}

.background-container img,
.background-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.background-overlay {
    position: absolute;
    inset: 0;
    height: 102%;
    background: linear-gradient(
        180deg,
        #000 0%,
        transparent 50%,
        #000 92%,
        #000 100%
    );
}

/* Particle Effect */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: particleFade 1s ease-out;
    pointer-events: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes particleFade {
    0% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .movie-year-section {
        padding: 60px 10px 0;
    }
    
    .movie-item {
        margin: 8px;
    }
    
    .movie-image-container {
        width: 80px;
        height: 112px;
    }
    
    .movie-info {
        width: 80px;
    }
}

@media (prefers-reduced-motion: reduce) {
    .movie-card,
    .movie-poster,
    .glow-overlay,
    .movie-title::after,
    .rating-pop,
    .done-slide,
    .movie-badge {
        transition: none;
    }
    
    .movie-year-section {
        animation: none;
    }
    
    .particle {
        animation: none;
        display: none;
    }
}