[{"title": "1987：黎明到来的那一天", "imgName": "1987：黎明到来的那一天", "aka": "1987：黎明到来的那一天", "rating": "被封了", "year": "2018", "done": "2018-02-10"}, {"title": "蜘蛛侠：平行宇宙 Spider-Man: Into the Spider-Verse", "imgName": "蜘蛛侠平行宇宙Spider-ManIntotheSpider-Verse", "aka": "蜘蛛侠：平行宇宙 Spider-Man: Into the Spider-Verse", "rating": "8.6", "year": "2018", "done": "2018-12-27"}, {"title": "超能陆战队 Big Hero 6", "imgName": "超能陆战队BigHero6", "aka": "超能陆战队 Big Hero 6", "rating": "8.7", "year": "2014", "done": "2018-12-27"}, {"title": "摄影机不要停！ カメラを止めるな！", "imgName": "摄影机不要停カメラを止めるな", "aka": "摄影机不要停！ カメラを止めるな！", "rating": "8.2", "year": "2017", "done": "2018-12-10"}, {"title": "印度合伙人 Padman", "imgName": "印度合伙人Padman", "aka": "印度合伙人 Padman", "rating": "7.7", "year": "2018", "done": "2018-12-09"}, {"title": "海王 Aquaman", "imgName": "海王Aquaman", "aka": "海王 Aquaman", "rating": "7.6", "year": "2018", "done": "2018-12-07"}, {"title": "无敌破坏王2：大闹互联网 Ralph Breaks the Internet", "imgName": "无敌破坏王2大闹互联网RalphBreakstheInternet", "aka": "无敌破坏王2：大闹互联网 Ralph Breaks the Internet", "rating": "8.0", "year": "2018", "done": "2018-12-01"}, {"title": "无敌破坏王 Wreck-It Ralph", "imgName": "无敌破坏王Wreck-ItRalph", "aka": "无敌破坏王 Wreck-It Ralph", "rating": "8.7", "year": "2012", "done": "2018-11-25"}, {"title": "无名之辈", "imgName": "无名之辈", "aka": "无名之辈", "rating": "8.0", "year": "2018", "done": "2018-11-24"}, {"title": "毒枭：墨西哥 第一季 Narcos: Mexico Season 1", "imgName": "毒枭墨西哥第一季NarcosMexicoSeason1", "aka": "毒枭：墨西哥 第一季 Narcos: Mexico Season 1", "rating": "9.1", "year": "2018", "done": "2018-11-17"}, {"title": "网络谜踪 Searching", "imgName": "网络谜踪Searching", "aka": "网络谜踪 Searching", "rating": "8.5", "year": "2018", "done": "2018-10-16"}, {"title": "江湖儿女", "imgName": "江湖儿女", "aka": "江湖儿女", "rating": "7.6", "year": "2018", "done": "2018-10-06"}, {"title": "现在去见你 지금 만나러 갑니다", "imgName": "现在去见你지금만나러갑니다", "aka": "现在去见你 지금 만나러 갑니다", "rating": "8.3", "year": "2018", "done": "2018-10-02"}, {"title": "影", "imgName": "影", "aka": "影", "rating": "7.2", "year": "2018", "done": "2018-09-30"}, {"title": "无双 無雙", "imgName": "无双無雙", "aka": "无双 無雙", "rating": "8.0", "year": "2018", "done": "2018-09-30"}, {"title": "黄海 황해", "imgName": "黄海황해", "aka": "黄海 황해", "rating": "8.5", "year": "2010", "done": "2018-09-29"}, {"title": "幸福的拉扎罗 <PERSON><PERSON><PERSON> felice", "imgName": "幸福的拉扎罗Lazzarofelice", "aka": "幸福的拉扎罗 <PERSON><PERSON><PERSON> felice", "rating": "8.5", "year": "2018", "done": "2018-09-29"}, {"title": "误杀瞒天记 Drishyam", "imgName": "误杀瞒天记Drishyam", "aka": "误杀瞒天记 Drishyam", "rating": "8.6", "year": "2015", "done": "2018-09-28"}, {"title": "特工 공작", "imgName": "特工공작", "aka": "特工 공작", "rating": "8.7", "year": "2018", "done": "2018-09-25"}, {"title": "恐怖直播 더 테러 라이브", "imgName": "恐怖直播더테러라이브", "aka": "恐怖直播 더 테러 라이브", "rating": "8.7", "year": "2013", "done": "2018-09-24"}, {"title": "解除好友2：暗网 Unfriended: Dark Web", "imgName": "解除好友2暗网UnfriendedDarkWeb", "aka": "解除好友2：暗网 Unfriended: Dark Web", "rating": "7.9", "year": "2018", "done": "2018-09-23"}, {"title": "与神同行2：因与缘 신과함께-인과 연", "imgName": "与神同行2因与缘신과함께-인과연", "aka": "与神同行2：因与缘 신과함께-인과 연", "rating": "7.6", "year": "2018", "done": "2018-09-22"}, {"title": "八恶人 The Hateful Eight", "imgName": "八恶人TheHatefulEight", "aka": "八恶人 The Hateful Eight", "rating": "8.6", "year": "2015", "done": "2018-09-20"}, {"title": "冒牌上尉 <PERSON>tmann", "imgName": "冒牌上尉Der<PERSON><PERSON><PERSON>mann", "aka": "冒牌上尉 <PERSON>tmann", "rating": "8.6", "year": "2017", "done": "2018-09-18"}, {"title": "胜者即是正义 リーガル・ハイ", "imgName": "胜者即是正义リーガル・ハイ", "aka": "胜者即是正义 リーガル・ハイ", "rating": "9.4", "year": "2012", "done": "2018-09-17"}, {"title": "血观音 血觀音", "imgName": "血观音血觀音", "aka": "血观音 血觀音", "rating": "8.5", "year": "2017", "done": "2018-09-15"}, {"title": "集结号", "imgName": "集结号", "aka": "集结号", "rating": "8.2", "year": "2007", "done": "2018-09-14"}, {"title": "传奇的诞生 Pelé: Birth of a Legend", "imgName": "传奇的诞生PeléBirthofaLegend", "aka": "传奇的诞生 Pelé: Birth of a Legend", "rating": "7.7", "year": "2016", "done": "2018-09-13"}, {"title": "大卫·戈尔的一生 The Life of David Gale", "imgName": "大卫·戈尔的一生TheLifeofDavidGale", "aka": "大卫·戈尔的一生 The Life of David Gale", "rating": "8.6", "year": "2003", "done": "2018-09-07"}, {"title": "风语咒", "imgName": "风语咒", "aka": "风语咒", "rating": "6.8", "year": "2018", "done": "2018-09-07"}, {"title": "完美的世界 A Perfect World", "imgName": "完美的世界APerfectWorld", "aka": "完美的世界 A Perfect World", "rating": "9.1", "year": "1993", "done": "2018-09-07"}, {"title": "犬之岛 Isle of Dogs", "imgName": "犬之岛IsleofDogs", "aka": "犬之岛 Isle of Dogs", "rating": "8.2", "year": "2018", "done": "2018-09-06"}, {"title": "黄石 第一季 Yellowstone Season 1", "imgName": "黄石第一季YellowstoneSeason1", "aka": "黄石 第一季 Yellowstone Season 1", "rating": "9.2", "year": "2018", "done": "2018-09-01"}, {"title": "碟中谍6：全面瓦解 Mission: Impossible - Fallout", "imgName": "碟中谍6全面瓦解MissionImpossible-Fallout", "aka": "碟中谍6：全面瓦解 Mission: Impossible - Fallout", "rating": "8.1", "year": "2018", "done": "2018-08-31"}, {"title": "荒野猎人 The Revenant", "imgName": "荒野猎人TheRevenant", "aka": "荒野猎人 The Revenant", "rating": "8.0", "year": "2015", "done": "2018-08-30"}, {"title": "升级 Upgrade", "imgName": "升级Upgrade", "aka": "升级 Upgrade", "rating": "8.2", "year": "2018", "done": "2018-08-29"}, {"title": "大师兄 大師兄", "imgName": "大师兄大師兄", "aka": "大师兄 大師兄", "rating": "5.1", "year": "2018", "done": "2018-08-24"}, {"title": "蚁人2：黄蜂女现身 <PERSON><PERSON>-Man and the <PERSON>p", "imgName": "蚁人2黄蜂女现身Ant-ManandtheWasp", "aka": "蚁人2：黄蜂女现身 <PERSON><PERSON>-Man and the <PERSON>p", "rating": "7.2", "year": "2018", "done": "2018-08-24"}, {"title": "风骚律师 第二季 Better Call Saul Season 2", "imgName": "风骚律师第二季BetterCallSaulSeason2", "aka": "风骚律师 第二季 Better Call Saul Season 2", "rating": "9.5", "year": "2016", "done": "2018-08-23"}, {"title": "风骚律师 第一季 Better Call Saul Season 1", "imgName": "风骚律师第一季BetterCallSaulSeason1", "aka": "风骚律师 第一季 Better Call Saul Season 1", "rating": "9.3", "year": "2015", "done": "2018-08-17"}, {"title": "精灵旅社3：疯狂假期 Hotel Transylvania 3: Summer Vacation", "imgName": "精灵旅社3疯狂假期HotelTransylvania3SummerVacation", "aka": "精灵旅社3：疯狂假期 Hotel Transylvania 3: Summer Vacation", "rating": "6.6", "year": "2018", "done": "2018-08-17"}, {"title": "精灵旅社2 Hotel Transylvania 2", "imgName": "精灵旅社2HotelTransylvania2", "aka": "精灵旅社2 Hotel Transylvania 2", "rating": "7.9", "year": "2015", "done": "2018-08-16"}, {"title": "精灵旅社 第一季 Hotel Transylvania Season 1", "imgName": "精灵旅社第一季HotelTransylvaniaSeason1", "aka": "精灵旅社 第一季 Hotel Transylvania Season 1", "rating": "7.9", "year": "2017", "done": "2018-08-15"}, {"title": "蚁人与黄蜂女：量子狂潮 Ant-Man and the Wasp: Quantumania", "imgName": "蚁人与黄蜂女量子狂潮Ant-ManandtheWaspQuantumania", "aka": "蚁人与黄蜂女：量子狂潮 Ant-Man and the Wasp: Quantumania", "rating": "5.8", "year": "2023", "done": "2018-08-14"}, {"title": "一出好戏", "imgName": "一出好戏", "aka": "一出好戏", "rating": "7.1", "year": "2018", "done": "2018-08-13"}, {"title": "狂怒 Fury", "imgName": "狂怒Fury", "aka": "狂怒 Fury", "rating": "8.0", "year": "2014", "done": "2018-08-12"}, {"title": "百万美元宝贝 Million Dollar Baby", "imgName": "百万美元宝贝MillionDollarBaby", "aka": "百万美元宝贝 Million Dollar Baby", "rating": "8.7", "year": "2004", "done": "2018-08-12"}, {"title": "卢旺达饭店 Hotel Rwanda", "imgName": "卢旺达饭店HotelRwanda", "aka": "卢旺达饭店 Hotel Rwanda", "rating": "8.9", "year": "2004", "done": "2018-08-10"}, {"title": "战争之王 Lord of War", "imgName": "战争之王Lordof<PERSON>ar", "aka": "战争之王 Lord of War", "rating": "8.7", "year": "2005", "done": "2018-08-09"}, {"title": "怦然心动 Flipped", "imgName": "怦然心动Flipped", "aka": "怦然心动 Flipped", "rating": "9.1", "year": "2010", "done": "2018-08-05"}, {"title": "死侍2：我爱我家 Deadpool 2", "imgName": "死侍2我爱我家Deadpool2", "aka": "死侍2：我爱我家 Deadpool 2", "rating": "7.3", "year": "2018", "done": "2018-07-28"}, {"title": "被解救的姜戈 Django Unchained", "imgName": "被解救的姜戈DjangoUnchained", "aka": "被解救的姜戈 Django Unchained", "rating": "8.8", "year": "2012", "done": "2018-07-28"}, {"title": "狄仁杰之四大天王", "imgName": "狄仁杰之四大天王", "aka": "狄仁杰之四大天王", "rating": "6.1", "year": "2018", "done": "2018-07-27"}, {"title": "大话西游之大圣娶亲 西遊記大結局之仙履奇緣", "imgName": "大话西游之大圣娶亲西遊記大結局之仙履奇緣", "aka": "大话西游之大圣娶亲 西遊記大結局之仙履奇緣", "rating": "9.2", "year": "1995", "done": "2018-07-26"}, {"title": "大话西游之月光宝盒 西遊記第壹佰零壹回之月光寶盒", "imgName": "大话西游之月光宝盒西遊記第壹佰零壹回之月光寶盒", "aka": "大话西游之月光宝盒 西遊記第壹佰零壹回之月光寶盒", "rating": "9.0", "year": "1995", "done": "2018-07-25"}, {"title": "喜剧之王 喜劇之王", "imgName": "喜剧之王喜劇之王", "aka": "喜剧之王 喜劇之王", "rating": "8.8", "year": "1999", "done": "2018-07-24"}, {"title": "大内密探零零发 大內密探零零發", "imgName": "大内密探零零发大內密探零零發", "aka": "大内密探零零发 大內密探零零發", "rating": "8.0", "year": "1996", "done": "2018-07-23"}, {"title": "功夫", "imgName": "功夫", "aka": "功夫", "rating": "8.8", "year": "2004", "done": "2018-07-22"}, {"title": "少林足球", "imgName": "少林足球", "aka": "少林足球", "rating": "8.1", "year": "2001", "done": "2018-07-21"}, {"title": "达拉斯买家俱乐部 Dallas Buyers Club", "imgName": "达拉斯买家俱乐部DallasBuyersClub", "aka": "达拉斯买家俱乐部 Dallas Buyers Club", "rating": "8.8", "year": "2013", "done": "2018-07-15"}, {"title": "邪不压正", "imgName": "邪不压正", "aka": "邪不压正", "rating": "7.0", "year": "2018", "done": "2018-07-13"}, {"title": "暹罗决：九神战甲 The Legend of Muay Thai: 9 Satra", "imgName": "暹罗决九神战甲TheLegendofMuayThai9Satra", "aka": "暹罗决：九神战甲 The Legend of Muay Thai: 9 Satra", "rating": "6.6", "year": "2018", "done": "2018-06-30"}, {"title": "我不是药神", "imgName": "我不是药神", "aka": "我不是药神", "rating": "9.0", "year": "2018", "done": "2018-06-30"}, {"title": "动物世界", "imgName": "动物世界", "aka": "动物世界", "rating": "7.2", "year": "2018", "done": "2018-06-29"}, {"title": "超人总动员2 Incredibles 2", "imgName": "超人总动员2Incredibles2", "aka": "超人总动员2 Incredibles 2", "rating": "7.8", "year": "2018", "done": "2018-06-22"}, {"title": "燃烧 버닝", "imgName": "燃烧버닝", "aka": "燃烧 버닝", "rating": "8.1", "year": "2018", "done": "2018-06-16"}, {"title": "奇异博士2：疯狂多元宇宙 Doctor Strange in the Multiverse of Madness", "imgName": "奇异博士2疯狂多元宇宙DoctorStrangeintheMultiverseofMadness", "aka": "奇异博士2：疯狂多元宇宙 Doctor Strange in the Multiverse of Madness", "rating": "6.3", "year": "2022", "done": "2018-06-12"}, {"title": "奇异博士 Doctor Strange", "imgName": "奇异博士DoctorStrange", "aka": "奇异博士 Doctor Strange", "rating": "7.6", "year": "2016", "done": "2018-06-12"}, {"title": "梅尔罗斯 <PERSON>", "imgName": "梅尔罗斯PatrickMelrose", "aka": "梅尔罗斯 <PERSON>", "rating": "9.0", "year": "2018", "done": "2018-05-27"}, {"title": "使女的故事 第一季 The Handmaid's Tale Season 1", "imgName": "使女的故事第一季TheHandmaidsTaleSeason1", "aka": "使女的故事 第一季 The Handmaid's Tale Season 1", "rating": "8.9", "year": "2017", "done": "2018-05-25"}, {"title": "毒枭 第三季 Narcos Season 3", "imgName": "毒枭第三季NarcosSeason3", "aka": "毒枭 第三季 Narcos Season 3", "rating": "9.4", "year": "2017", "done": "2018-05-01"}, {"title": "毒枭 第二季 Narcos Season 2", "imgName": "毒枭第二季NarcosSeason2", "aka": "毒枭 第二季 Narcos Season 2", "rating": "9.4", "year": "2016", "done": "2018-04-28"}, {"title": "毒枭 第一季 Narcos Season 1", "imgName": "毒枭第一季NarcosSeason1", "aka": "毒枭 第一季 Narcos Season 1", "rating": "9.3", "year": "2015", "done": "2018-04-28"}, {"title": "后来的我们", "imgName": "后来的我们", "aka": "后来的我们", "rating": "5.9", "year": "2018", "done": "2018-04-28"}, {"title": "华盛顿邮报 The Post", "imgName": "华盛顿邮报ThePost", "aka": "华盛顿邮报 The Post", "rating": "8.3", "year": "2017", "done": "2018-04-07"}, {"title": "暴裂无声", "imgName": "暴裂无声", "aka": "暴裂无声", "rating": "8.2", "year": "2017", "done": "2018-04-06"}, {"title": "头号玩家 Ready Player One", "imgName": "头号玩家ReadyPlayerOne", "aka": "头号玩家 Ready Player One", "rating": "8.7", "year": "2018", "done": "2018-03-30"}, {"title": "大坏狐狸的故事 Le Grand Méchant Renard et autres contes...", "imgName": "大坏狐狸的故事LeGrandMéchantRenardetautrescontes", "aka": "大坏狐狸的故事 Le Grand Méchant Renard et autres contes...", "rating": "8.3", "year": "2017", "done": "2018-03-30"}, {"title": "水形物语 The Shape of Water", "imgName": "水形物语TheShapeofWater", "aka": "水形物语 The Shape of Water", "rating": "7.2", "year": "2017", "done": "2018-03-15"}, {"title": "大佛普拉斯", "imgName": "大佛普拉斯", "aka": "大佛普拉斯", "rating": "8.7", "year": "2017", "done": "2018-03-05"}, {"title": "钢琴家 The Pianist", "imgName": "钢琴家ThePianist", "aka": "钢琴家 The Pianist", "rating": "9.3", "year": "2002", "done": "2018-03-04"}, {"title": "小萝莉的猴神大叔 <PERSON><PERSON><PERSON><PERSON>", "imgName": "小萝莉的猴神大叔Bajrangi<PERSON>haijaan", "aka": "小萝莉的猴神大叔 <PERSON><PERSON><PERSON><PERSON>", "rating": "8.4", "year": "2015", "done": "2018-03-01"}, {"title": "机智牢房生活 슬기로운 감빵생활", "imgName": "机智牢房生活슬기로운감빵생활", "aka": "机智牢房生活 슬기로운 감빵생활", "rating": "9.4", "year": "2017", "done": "2018-02-25"}, {"title": "唐人街探案2", "imgName": "唐人街探案2", "aka": "唐人街探案2", "rating": "", "year": "2023", "done": "2018-02-25"}, {"title": "红海行动", "imgName": "红海行动", "aka": "红海行动", "rating": "8.2", "year": "2018", "done": "2018-02-23"}, {"title": "伯德小姐 Lady Bird", "imgName": "伯德小姐LadyBird", "aka": "伯德小姐 Lady Bird", "rating": "7.9", "year": "2017", "done": "2018-02-03"}, {"title": "南极之恋", "imgName": "南极之恋", "aka": "南极之恋", "rating": "6.6", "year": "2018", "done": "2018-02-02"}, {"title": "撞车 Crash", "imgName": "撞车Crash", "aka": "撞车 Crash", "rating": "8.6", "year": "2004", "done": "2018-01-28"}, {"title": "三块广告牌 Three Billboards Outside Ebbing, Missouri", "imgName": "三块广告牌ThreeBillboardsOutsideEbbingMissouri", "aka": "三块广告牌 Three Billboards Outside Ebbing, Missouri", "rating": "8.7", "year": "2017", "done": "2018-01-27"}, {"title": "奇迹男孩 Wonder", "imgName": "奇迹男孩Wonder", "aka": "奇迹男孩 Wonder", "rating": "8.6", "year": "2017", "done": "2018-01-26"}, {"title": "铁雨 강철비", "imgName": "铁雨강철비", "aka": "铁雨 강철비", "rating": "8.2", "year": "2017", "done": "2018-01-21"}, {"title": "神秘巨星 Secret Superstar", "imgName": "神秘巨星SecretSuperstar", "aka": "神秘巨星 Secret Superstar", "rating": "7.7", "year": "2017", "done": "2018-01-20"}, {"title": "无问西东", "imgName": "无问西东", "aka": "无问西东", "rating": "7.5", "year": "2018", "done": "2018-01-15"}, {"title": "兵临城下 Enemy at the Gates", "imgName": "兵临城下EnemyattheGates", "aka": "兵临城下 Enemy at the Gates", "rating": "8.4", "year": "2001", "done": "2018-01-07"}]