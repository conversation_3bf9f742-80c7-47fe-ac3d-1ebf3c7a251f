[{"title": "画江湖之天罡", "imgName": "画江湖之天罡", "aka": "画江湖之天罡", "rating": "8.1", "year": "2023", "done": "2023-12-31"}, {"title": "想见你 想見你", "imgName": "想见你想見你", "aka": "想见你 想見你", "rating": "9.2", "year": "2019", "done": "2023-12-31"}, {"title": "当男人恋爱时 남자가 사랑할 때", "imgName": "当男人恋爱时남자가사랑할때", "aka": "当男人恋爱时 남자가 사랑할 때", "rating": "8.0", "year": "2014", "done": "2023-12-30"}, {"title": "流人 第三季 Slow Horses Season 3", "imgName": "流人第三季SlowHorsesSeason3", "aka": "流人 第三季 Slow Horses Season 3", "rating": "9.2", "year": "2023", "done": "2023-12-29"}, {"title": "青蛇", "imgName": "青蛇", "aka": "青蛇", "rating": "8.6", "year": "1993", "done": "2023-12-29"}, {"title": "仁医 完结篇 JIN-仁2", "imgName": "仁医完结篇JIN-仁2", "aka": "仁医 完结篇 JIN-仁2", "rating": "9.4", "year": "2011", "done": "2023-12-28"}, {"title": "仁医 JIN-仁-", "imgName": "仁医JIN-仁-", "aka": "仁医 JIN-仁-", "rating": "9.1", "year": "2009", "done": "2023-12-26"}, {"title": "坡道上的家 坂の途中の家", "imgName": "坡道上的家坂の途中の家", "aka": "坡道上的家 坂の途中の家", "rating": "9.0", "year": "2019", "done": "2023-12-24"}, {"title": "倩女幽魂3：道道道 倩女幽魂Ⅲ 道道道", "imgName": "倩女幽魂3道道道倩女幽魂Ⅲ道道道", "aka": "倩女幽魂3：道道道 倩女幽魂Ⅲ 道道道", "rating": "7.6", "year": "1991", "done": "2023-12-24"}, {"title": "小森林 夏秋篇 リトル・フォレスト 夏・秋", "imgName": "小森林夏秋篇リトル・フォレスト夏・秋", "aka": "小森林 夏秋篇 リトル・フォレスト 夏・秋", "rating": "9.0", "year": "2014", "done": "2023-12-23"}, {"title": "倩女幽魂2：人间道 倩女幽魂II 人間道", "imgName": "倩女幽魂2人间道倩女幽魂II人間道", "aka": "倩女幽魂2：人间道 倩女幽魂II 人間道", "rating": "8.1", "year": "1990", "done": "2023-12-23"}, {"title": "倩女幽魂", "imgName": "倩女幽魂", "aka": "倩女幽魂", "rating": "8.8", "year": "1987", "done": "2023-12-23"}, {"title": "月球叛军：火之女 Rebel Moon: A Child of Fire", "imgName": "月球叛军火之女RebelMoonAChildofFire", "aka": "月球叛军：火之女 Rebel Moon: A Child of Fire", "rating": "5.4", "year": "2023", "done": "2023-12-22"}, {"title": "英雄", "imgName": "英雄", "aka": "英雄", "rating": "7.6", "year": "2002", "done": "2023-12-21"}, {"title": "一代宗师 一代宗師", "imgName": "一代宗师一代宗師", "aka": "一代宗师 一代宗師", "rating": "8.2", "year": "2013", "done": "2023-12-20"}, {"title": "卧虎藏龙 臥虎藏龍", "imgName": "卧虎藏龙臥虎藏龍", "aka": "卧虎藏龙 臥虎藏龍", "rating": "8.4", "year": "2000", "done": "2023-12-19"}, {"title": "新龙门客栈 新龍門客棧", "imgName": "新龙门客栈新龍門客棧", "aka": "新龙门客栈 新龍門客棧", "rating": "8.7", "year": "1992", "done": "2023-12-18"}, {"title": "东邪西毒：终极版 東邪西毒終極版", "imgName": "东邪西毒终极版東邪西毒終極版", "aka": "东邪西毒：终极版 東邪西毒終極版", "rating": "8.8", "year": "2008", "done": "2023-12-17"}, {"title": "我的恐怖妻子 僕のヤバイ妻", "imgName": "我的恐怖妻子僕のヤバイ妻", "aka": "我的恐怖妻子 僕のヤバイ妻", "rating": "9.1", "year": "2016", "done": "2023-12-17"}, {"title": "射雕英雄传之东成西就 射鵰英雄傳之東成西就", "imgName": "射雕英雄传之东成西就射鵰英雄傳之東成西就", "aka": "射雕英雄传之东成西就 射鵰英雄傳之東成西就", "rating": "8.7", "year": "1993", "done": "2023-12-16"}, {"title": "甜蜜蜜", "imgName": "甜蜜蜜", "aka": "甜蜜蜜", "rating": "8.9", "year": "1996", "done": "2023-12-16"}, {"title": "剑雨", "imgName": "剑雨", "aka": "剑雨", "rating": "7.7", "year": "2010", "done": "2023-12-15"}, {"title": "荒岛余生 Cast Away", "imgName": "荒岛余生CastAway", "aka": "荒岛余生 Cast Away", "rating": "8.6", "year": "2000", "done": "2023-12-14"}, {"title": "入殓师 おくりびと", "imgName": "入殓师おくりびと", "aka": "入殓师 おくりびと", "rating": "8.9", "year": "2008", "done": "2023-12-13"}, {"title": "阳光姐妹淘 써니", "imgName": "阳光姐妹淘써니", "aka": "阳光姐妹淘 써니", "rating": "8.8", "year": "2011", "done": "2023-12-10"}, {"title": "河边的错误", "imgName": "河边的错误", "aka": "河边的错误", "rating": "7.3", "year": "2023", "done": "2023-12-09"}, {"title": "洛基 第二季 Loki Season 2", "imgName": "洛基第二季LokiSeason2", "aka": "洛基 第二季 Loki Season 2", "rating": "9.0", "year": "2023", "done": "2023-12-09"}, {"title": "花月杀手 Killers of the Flower Moon", "imgName": "花月杀手KillersoftheFlowerMoon", "aka": "花月杀手 Killers of the Flower Moon", "rating": "7.3", "year": "2023", "done": "2023-12-06"}, {"title": "弱点 The Blind Side", "imgName": "弱点TheBlindSide", "aka": "弱点 The Blind Side", "rating": "8.7", "year": "2009", "done": "2023-12-04"}, {"title": "大鱼 Big Fish", "imgName": "大鱼BigFish", "aka": "大鱼 Big Fish", "rating": "8.8", "year": "2003", "done": "2023-12-03"}, {"title": "非自然死亡 アンナチュラル", "imgName": "非自然死亡アンナチュラル", "aka": "非自然死亡 アンナチュラル", "rating": "9.5", "year": "2018", "done": "2023-12-03"}, {"title": "灵能百分百 Ⅲ モブサイコ100 III", "imgName": "灵能百分百Ⅲモブサイコ100III", "aka": "灵能百分百 Ⅲ モブサイコ100 III", "rating": "9.5", "year": "2022", "done": "2023-12-01"}, {"title": "重启人生 ブラッシュアップライフ", "imgName": "重启人生ブラッシュアップライフ", "aka": "重启人生 ブラッシュアップライフ", "rating": "9.4", "year": "2023", "done": "2023-11-29"}, {"title": "灵能百分百 II モブサイコ100 II", "imgName": "灵能百分百IIモブサイコ100II", "aka": "灵能百分百 II モブサイコ100 II", "rating": "9.6", "year": "2019", "done": "2023-11-27"}, {"title": "灵能百分百 モブサイコ100", "imgName": "灵能百分百モブサイコ100", "aka": "灵能百分百 モブサイコ100", "rating": "9.4", "year": "2016", "done": "2023-11-25"}, {"title": "Move to Heaven：我是遗物整理师 무브 투 헤븐: 나는 유품 정리사입니다", "imgName": "MovetoHeaven我是遗物整理师무브투헤븐나는유품정리사입니다", "aka": "Move to Heaven：我是遗物整理师 무브 투 헤븐: 나는 유품 정리사입니다", "rating": "9.1", "year": "2021", "done": "2023-11-25"}, {"title": "拾荒者统治 Scavengers Reign", "imgName": "拾荒者统治ScavengersReign", "aka": "拾荒者统治 Scavengers Reign", "rating": "9.3", "year": "2023", "done": "2023-11-19"}, {"title": "蓝眼武士 Blue Eye Samurai", "imgName": "蓝眼武士BlueEyeSamurai", "aka": "蓝眼武士 Blue Eye Samurai", "rating": "8.8", "year": "2023", "done": "2023-11-17"}, {"title": "1947波士顿 1947 보스톤", "imgName": "1947波士顿1947보스톤", "aka": "1947波士顿 1947 보스톤", "rating": "7.4", "year": "2023", "done": "2023-11-14"}, {"title": "繁城之下", "imgName": "繁城之下", "aka": "繁城之下", "rating": "8.5", "year": "2023", "done": "2023-11-12"}, {"title": "春光乍泄 春光乍洩", "imgName": "春光乍泄春光乍洩", "aka": "春光乍泄 春光乍洩", "rating": "9.0", "year": "1997", "done": "2023-11-07"}, {"title": "花样年华 花樣年華", "imgName": "花样年华花樣年華", "aka": "花样年华 花樣年華", "rating": "8.8", "year": "2000", "done": "2023-11-06"}, {"title": "帝国的毁灭 Der Untergang", "imgName": "帝国的毁灭DerUntergang", "aka": "帝国的毁灭 Der Untergang", "rating": "8.9", "year": "2004", "done": "2023-11-05"}, {"title": "V世代 第一季 Gen V Season 1", "imgName": "V世代第一季GenVSeason1", "aka": "V世代 第一季 Gen V Season 1", "rating": "8.3", "year": "2023", "done": "2023-11-05"}, {"title": "火线 第三季 The Wire Season 3", "imgName": "火线第三季TheWireSeason3", "aka": "火线 第三季 The Wire Season 3", "rating": "9.6", "year": "2004", "done": "2023-11-04"}, {"title": "朝圣之路 The Way", "imgName": "朝圣之路TheWay", "aka": "朝圣之路 The Way", "rating": "8.4", "year": "2010", "done": "2023-10-31"}, {"title": "刺杀肯尼迪 JFK", "imgName": "刺杀肯尼迪JFK", "aka": "刺杀肯尼迪 JFK", "rating": "8.8", "year": "1991", "done": "2023-10-30"}, {"title": "重庆森林 重慶森林", "imgName": "重庆森林重慶森林", "aka": "重庆森林 重慶森林", "rating": "8.8", "year": "1994", "done": "2023-10-29"}, {"title": "阿甘正传 <PERSON>", "imgName": "阿甘正传ForrestGump", "aka": "阿甘正传 <PERSON>", "rating": "9.5", "year": "1994", "done": "2023-10-26"}, {"title": "敦刻尔克 Dunkirk", "imgName": "敦刻尔克Dunkirk", "aka": "敦刻尔克 Dunkirk", "rating": "8.4", "year": "2017", "done": "2023-10-25"}, {"title": "火线 第二季 The Wire Season 2", "imgName": "火线第二季TheWireSeason2", "aka": "火线 第二季 The Wire Season 2", "rating": "9.6", "year": "2003", "done": "2023-10-24"}, {"title": "火线 第一季 The Wire Season 1", "imgName": "火线第一季TheWireSeason1", "aka": "火线 第一季 The Wire Season 1", "rating": "9.4", "year": "2002", "done": "2023-10-22"}, {"title": "纽约灾星 The Jinx: The Life and Deaths of <PERSON>", "imgName": "纽约灾星TheJinxTheLifeandDeathsofRobertDurst", "aka": "纽约灾星 The Jinx: The Life and Deaths of <PERSON>", "rating": "9.1", "year": "2015", "done": "2023-10-20"}, {"title": "红辣椒 パプリカ", "imgName": "红辣椒パプリカ", "aka": "红辣椒 パプリカ", "rating": "9.1", "year": "2006", "done": "2023-10-19"}, {"title": "万神殿 第二季 Pantheon Season 2", "imgName": "万神殿第二季PantheonSeason2", "aka": "万神殿 第二季 Pantheon Season 2", "rating": "9.3", "year": "2023", "done": "2023-10-18"}, {"title": "坠楼死亡的剖析 Anatomie d’une chute", "imgName": "坠楼死亡的剖析Anatomied’unechute", "aka": "坠楼死亡的剖析 Anatomie d’une chute", "rating": "8.4", "year": "2023", "done": "2023-10-16"}, {"title": "巴比伦柏林 第四季 Babylon Berlin Season 4", "imgName": "巴比伦柏林第四季BabylonBerlinSeason4", "aka": "巴比伦柏林 第四季 Babylon Berlin Season 4", "rating": "9.1", "year": "2022", "done": "2023-10-15"}, {"title": "巴比伦柏林 第三季 Babylon Berlin Season 3", "imgName": "巴比伦柏林第三季BabylonBerlinSeason3", "aka": "巴比伦柏林 第三季 Babylon Berlin Season 3", "rating": "9.2", "year": "2020", "done": "2023-10-15"}, {"title": "巴比伦柏林 第二季 Babylon Berlin Season 2", "imgName": "巴比伦柏林第二季BabylonBerlinSeason2", "aka": "巴比伦柏林 第二季 Babylon Berlin Season 2", "rating": "9.4", "year": "2017", "done": "2023-10-14"}, {"title": "巴比伦柏林 第一季 Babylon Berlin Season 1", "imgName": "巴比伦柏林第一季BabylonBerlinSeason1", "aka": "巴比伦柏林 第一季 Babylon Berlin Season 1", "rating": "9.1", "year": "2017", "done": "2023-10-13"}, {"title": "汉尼拔 Hannibal", "imgName": "汉尼拔Hannibal", "aka": "汉尼拔 Hannibal", "rating": "8.3", "year": "2001", "done": "2023-10-12"}, {"title": "至暗时刻 Darkest Hour", "imgName": "至暗时刻DarkestHour", "aka": "至暗时刻 Darkest Hour", "rating": "8.5", "year": "2017", "done": "2023-10-11"}, {"title": "雾山五行·犀川幻紫林", "imgName": "雾山五行·犀川幻紫林", "aka": "雾山五行·犀川幻紫林", "rating": "9.3", "year": "2023", "done": "2023-10-10"}, {"title": "雾山五行", "imgName": "雾山五行", "aka": "雾山五行", "rating": "8.7", "year": "2020", "done": "2023-10-10"}, {"title": "雨人 Rain Man", "imgName": "雨人<PERSON>an", "aka": "雨人 Rain Man", "rating": "8.7", "year": "1988", "done": "2023-10-09"}, {"title": "过往人生 Past Lives", "imgName": "过往人生PastLives", "aka": "过往人生 Past Lives", "rating": "7.7", "year": "2023", "done": "2023-10-07"}, {"title": "八角笼中", "imgName": "八角笼中", "aka": "八角笼中", "rating": "7.3", "year": "2023", "done": "2023-10-06"}, {"title": "请回答1988 응답하라 1988", "imgName": "请回答1988응답하라1988", "aka": "请回答1988 응답하라 1988", "rating": "9.7", "year": "2015", "done": "2023-10-05"}, {"title": "自由之声 Sound of Freedom", "imgName": "自由之声SoundofFreedom", "aka": "自由之声 Sound of Freedom", "rating": "8.0", "year": "2023", "done": "2023-10-04"}, {"title": "好像也没那么热血沸腾", "imgName": "好像也没那么热血沸腾", "aka": "好像也没那么热血沸腾", "rating": "7.0", "year": "2023", "done": "2023-10-03"}, {"title": "被解救的姜戈 Django Unchained", "imgName": "被解救的姜戈DjangoUnchained", "aka": "被解救的姜戈 Django Unchained", "rating": "8.8", "year": "2012", "done": "2023-10-02"}, {"title": "美国往事 Once Upon a Time in America", "imgName": "美国往事OnceUponaTimeinAmerica", "aka": "美国往事 Once Upon a Time in America", "rating": "9.2", "year": "1984", "done": "2023-10-02"}, {"title": "疯狂的麦克斯4：狂暴之路 Mad Max: Fury Road", "imgName": "疯狂的麦克斯4狂暴之路MadMaxFuryRoad", "aka": "疯狂的麦克斯4：狂暴之路 Mad Max: Fury Road", "rating": "8.7", "year": "2015", "done": "2023-10-01"}, {"title": "坚如磐石", "imgName": "坚如磐石", "aka": "坚如磐石", "rating": "6.3", "year": "2023", "done": "2023-09-30"}, {"title": "天下无贼", "imgName": "天下无贼", "aka": "天下无贼", "rating": "8.1", "year": "2004", "done": "2023-09-30"}, {"title": "93国际列车大劫案：莫斯科行动", "imgName": "93国际列车大劫案莫斯科行动", "aka": "93国际列车大劫案：莫斯科行动", "rating": "6.7", "year": "2023", "done": "2023-09-30"}, {"title": "长安三万里", "imgName": "长安三万里", "aka": "长安三万里", "rating": "8.3", "year": "2023", "done": "2023-09-29"}, {"title": "走私 밀수", "imgName": "走私밀수", "aka": "走私 밀수", "rating": "6.8", "year": "2023", "done": "2023-09-28"}, {"title": "山道猴子的一生", "imgName": "山道猴子的一生", "aka": "山道猴子的一生", "rating": "", "year": "2023", "done": "2023-09-27"}, {"title": "千年女优 千年女優", "imgName": "千年女优千年女優", "aka": "千年女优 千年女優", "rating": "8.8", "year": "2001", "done": "2023-09-26"}, {"title": "绝地追击", "imgName": "绝地追击", "aka": "绝地追击", "rating": "6.6", "year": "2023", "done": "2023-09-25"}, {"title": "超异能族 무빙", "imgName": "超异能族무빙", "aka": "超异能族 무빙", "rating": "8.9", "year": "2023", "done": "2023-09-24"}, {"title": "封神第一部：朝歌风云", "imgName": "封神第一部朝歌风云", "aka": "封神第一部：朝歌风云", "rating": "7.9", "year": "2023", "done": "2023-09-22"}, {"title": "茶啊二中", "imgName": "茶啊二中", "aka": "茶啊二中", "rating": "7.4", "year": "2023", "done": "2023-09-20"}, {"title": "亿万 第四季 Billions Season 4", "imgName": "亿万第四季BillionsSeason4", "aka": "亿万 第四季 Billions Season 4", "rating": "9.3", "year": "2019", "done": "2023-09-19"}, {"title": "亿万 第三季 Billions Season 3", "imgName": "亿万第三季BillionsSeason3", "aka": "亿万 第三季 Billions Season 3", "rating": "9.2", "year": "2018", "done": "2023-09-16"}, {"title": "奥本海默 <PERSON><PERSON><PERSON>", "imgName": "奥本海默<PERSON><PERSON><PERSON>", "aka": "奥本海默 <PERSON><PERSON><PERSON>", "rating": "8.9", "year": "2023", "done": "2023-09-09"}, {"title": "大空头 The Big Short", "imgName": "大空头TheBigShort", "aka": "大空头 The Big Short", "rating": "8.5", "year": "2015", "done": "2023-09-06"}, {"title": "林肯律师 The Lincoln Lawyer", "imgName": "林肯律师TheLincolnLawyer", "aka": "林肯律师 The Lincoln Lawyer", "rating": "7.9", "year": "2011", "done": "2023-09-05"}, {"title": "芭比 Barbie", "imgName": "芭比Barbie", "aka": "芭比 Barbie", "rating": "8.1", "year": "2023", "done": "2023-09-04"}, {"title": "罗马 第二季 Rome Season 2", "imgName": "罗马第二季RomeSeason2", "aka": "罗马 第二季 Rome Season 2", "rating": "9.0", "year": "2007", "done": "2023-09-03"}, {"title": "罗马 第一季 Rome Season 1", "imgName": "罗马第一季RomeSeason1", "aka": "罗马 第一季 Rome Season 1", "rating": "9.0", "year": "2005", "done": "2023-09-02"}, {"title": "亿万 第二季 Billions Season 2", "imgName": "亿万第二季BillionsSeason2", "aka": "亿万 第二季 Billions Season 2", "rating": "9.3", "year": "2017", "done": "2023-08-30"}, {"title": "未麻的部屋 Perfect Blue", "imgName": "未麻的部屋PerfectBlue", "aka": "未麻的部屋 Perfect Blue", "rating": "9.1", "year": "1997", "done": "2023-08-27"}, {"title": "沉默的羔羊 The Silence of the Lambs", "imgName": "沉默的羔羊TheSilenceoftheLambs", "aka": "沉默的羔羊 The Silence of the Lambs", "rating": "8.9", "year": "1991", "done": "2023-08-27"}, {"title": "蝴蝶效应 The Butterfly Effect", "imgName": "蝴蝶效应TheButterflyEffect", "aka": "蝴蝶效应 The Butterfly Effect", "rating": "8.9", "year": "2004", "done": "2023-08-26"}, {"title": "死亡诗社 Dead Poets Society", "imgName": "死亡诗社DeadPoetsSociety", "aka": "死亡诗社 Dead Poets Society", "rating": "9.2", "year": "1989", "done": "2023-08-24"}, {"title": "东京教父 東京ゴッドファーザーズ", "imgName": "东京教父東京ゴッドファーザーズ", "aka": "东京教父 東京ゴッドファーザーズ", "rating": "9.0", "year": "2003", "done": "2023-08-22"}, {"title": "火山挚恋 Fire of Love", "imgName": "火山挚恋FireofLove", "aka": "火山挚恋 Fire of Love", "rating": "9.0", "year": "2022", "done": "2023-08-21"}, {"title": "镖人 第一季 镖人", "imgName": "镖人第一季镖人", "aka": "镖人 第一季 镖人", "rating": "8.7", "year": "2023", "done": "2023-08-20"}, {"title": "亿万 第一季 Billions Season 1", "imgName": "亿万第一季BillionsSeason1", "aka": "亿万 第一季 Billions Season 1", "rating": "8.9", "year": "2016", "done": "2023-08-20"}, {"title": "关于我和鬼变成家人的那件事 關於我和鬼變成家人的那件事", "imgName": "关于我和鬼变成家人的那件事關於我和鬼變成家人的那件事", "aka": "关于我和鬼变成家人的那件事 關於我和鬼變成家人的那件事", "rating": "8.2", "year": "2022", "done": "2023-08-15"}, {"title": "教父3 The Godfather: Part III", "imgName": "教父3TheGodfatherPartIII", "aka": "教父3 The Godfather: Part III", "rating": "9.0", "year": "1990", "done": "2023-08-13"}, {"title": "教父2 The Godfather: Part II", "imgName": "教父2TheGodfatherPartII", "aka": "教父2 The Godfather: Part II", "rating": "9.3", "year": "1974", "done": "2023-08-13"}, {"title": "教父 The Godfather", "imgName": "教父The<PERSON>odfather", "aka": "教父 The Godfather", "rating": "9.3", "year": "1972", "done": "2023-08-12"}, {"title": "蜘蛛侠：纵横宇宙 Spider-Man: Across the Spider-Verse", "imgName": "蜘蛛侠纵横宇宙Spider-ManAcrosstheSpider-Verse", "aka": "蜘蛛侠：纵横宇宙 Spider-Man: Across the Spider-Verse", "rating": "8.5", "year": "2023", "done": "2023-08-11"}, {"title": "天使爱美丽 Le fabuleux destin d'<PERSON>", "imgName": "天使爱美丽LefabuleuxdestindAméliePoulain", "aka": "天使爱美丽 Le fabuleux destin d'<PERSON>", "rating": "8.7", "year": "2001", "done": "2023-08-10"}, {"title": "我的天才女友 第三季 L'amica geniale Season 3", "imgName": "我的天才女友第三季LamicagenialeSeason3", "aka": "我的天才女友 第三季 L'amica geniale Season 3", "rating": "9.5", "year": "2022", "done": "2023-08-09"}, {"title": "我的天才女友 第二季 L'amica geniale Season 2", "imgName": "我的天才女友第二季LamicagenialeSeason2", "aka": "我的天才女友 第二季 L'amica geniale Season 2", "rating": "9.5", "year": "2020", "done": "2023-08-06"}, {"title": "我的天才女友 第一季 L'amica geniale Season 1", "imgName": "我的天才女友第一季LamicagenialeSeason1", "aka": "我的天才女友 第一季 L'amica geniale Season 1", "rating": "9.4", "year": "2018", "done": "2023-08-05"}, {"title": "戴洛克小镇 第一季 Deadloch Season 1", "imgName": "戴洛克小镇第一季DeadlochSeason1", "aka": "戴洛克小镇 第一季 Deadloch Season 1", "rating": "9.1", "year": "2023", "done": "2023-08-05"}, {"title": "电锯惊魂 Saw", "imgName": "电锯惊魂Saw", "aka": "电锯惊魂 Saw", "rating": "8.8", "year": "2004", "done": "2023-08-02"}, {"title": "被嫌弃的松子的一生 嫌われ松子の一生", "imgName": "被嫌弃的松子的一生嫌われ松子の一生", "aka": "被嫌弃的松子的一生 嫌われ松子の一生", "rating": "8.9", "year": "2006", "done": "2023-08-01"}, {"title": "末路狂花 Thelma & Louise", "imgName": "末路狂花ThelmaLouise", "aka": "末路狂花 Thelma & Louise", "rating": "8.9", "year": "1991", "done": "2023-07-30"}, {"title": "猎杀U-571 U-571", "imgName": "猎杀U-571U-571", "aka": "猎杀U-571 U-571", "rating": "8.1", "year": "2000", "done": "2023-07-29"}, {"title": "戏梦空间 The Ordinaries", "imgName": "戏梦空间TheOrdinaries", "aka": "戏梦空间 The Ordinaries", "rating": "7.9", "year": "2022", "done": "2023-07-29"}, {"title": "黑鹰坠落 Black Hawk Down", "imgName": "黑鹰坠落BlackHawkDown", "aka": "黑鹰坠落 Black Hawk Down", "rating": "8.7", "year": "2001", "done": "2023-07-28"}, {"title": "恐怖游轮 Triangle", "imgName": "恐怖游轮Triangle", "aka": "恐怖游轮 Triangle", "rating": "8.5", "year": "2009", "done": "2023-07-27"}, {"title": "海边的曼彻斯特 Manchester by the Sea", "imgName": "海边的曼彻斯特ManchesterbytheSea", "aka": "海边的曼彻斯特 Manchester by the Sea", "rating": "8.6", "year": "2016", "done": "2023-07-26"}, {"title": "金币灰黄 برادران لیلا", "imgName": "金币灰黄برادرانلیلا", "aka": "金币灰黄 برادران لیلا", "rating": "8.1", "year": "2022", "done": "2023-07-24"}, {"title": "命案", "imgName": "命案", "aka": "命案", "rating": "7.4", "year": "2023", "done": "2023-07-24"}, {"title": "我们的父辈 <PERSON><PERSON><PERSON>, unsere <PERSON>", "imgName": "我们的父辈UnsereMütterunsereVäter", "aka": "我们的父辈 <PERSON><PERSON><PERSON>, unsere <PERSON>", "rating": "9.6", "year": "2013", "done": "2023-07-23"}, {"title": "太平洋战争 The Pacific", "imgName": "太平洋战争ThePacific", "aka": "太平洋战争 The Pacific", "rating": "9.0", "year": "2010", "done": "2023-07-23"}, {"title": "思悼 사도", "imgName": "思悼사도", "aka": "思悼 사도", "rating": "8.5", "year": "2015", "done": "2023-07-21"}, {"title": "全金属外壳 Full Metal Jacket", "imgName": "全金属外壳FullMetalJacket", "aka": "全金属外壳 Full Metal Jacket", "rating": "8.6", "year": "1987", "done": "2023-07-20"}, {"title": "闪电侠 The Flash", "imgName": "闪电侠TheFlash", "aka": "闪电侠 The Flash", "rating": "7.8", "year": "2023", "done": "2023-07-19"}, {"title": "绿里奇迹 The Green Mile", "imgName": "绿里奇迹TheGreenMile", "aka": "绿里奇迹 The Green Mile", "rating": "8.9", "year": "1999", "done": "2023-07-18"}, {"title": "黑天鹅 Black Swan", "imgName": "黑天鹅BlackSwan", "aka": "黑天鹅 Black Swan", "rating": "8.6", "year": "2010", "done": "2023-07-16"}, {"title": "谍影重重3 The Bourne Ultimatum", "imgName": "谍影重重3TheBourneUltimatum", "aka": "谍影重重3 The Bourne Ultimatum", "rating": "8.8", "year": "2007", "done": "2023-07-16"}, {"title": "谍影重重2 The Bourne Supremacy", "imgName": "谍影重重2TheBourneSupremacy", "aka": "谍影重重2 The Bourne Supremacy", "rating": "8.7", "year": "2004", "done": "2023-07-15"}, {"title": "谍影重重 The Bourne Identity", "imgName": "谍影重重TheBourneIdentity", "aka": "谍影重重 The Bourne Identity", "rating": "8.6", "year": "2002", "done": "2023-07-15"}, {"title": "告白", "imgName": "告白", "aka": "告白", "rating": "8.8", "year": "2010", "done": "2023-07-15"}, {"title": "下一个素熙 다음 소희", "imgName": "下一个素熙다음소희", "aka": "下一个素熙 다음 소희", "rating": "8.3", "year": "2022", "done": "2023-07-14"}, {"title": "继承之战 第四季 Succession Season 4", "imgName": "继承之战第四季SuccessionSeason4", "aka": "继承之战 第四季 Succession Season 4", "rating": "9.4", "year": "2023", "done": "2023-07-13"}, {"title": "曼达洛人 第三季 The Mandalorian Season 3", "imgName": "曼达洛人第三季TheMandalorianSeason3", "aka": "曼达洛人 第三季 The Mandalorian Season 3", "rating": "9.0", "year": "2023", "done": "2023-07-11"}, {"title": "可可西里", "imgName": "可可西里", "aka": "可可西里", "rating": "8.9", "year": "2004", "done": "2023-06-29"}, {"title": "消失的爱人 Gone Girl", "imgName": "消失的爱人GoneGirl", "aka": "消失的爱人 Gone Girl", "rating": "8.7", "year": "2014", "done": "2023-06-25"}, {"title": "毒舌律师 毒舌大狀", "imgName": "毒舌律师毒舌大狀", "aka": "毒舌律师 毒舌大狀", "rating": "7.5", "year": "2023", "done": "2023-05-27"}, {"title": "启示 Apocalypto", "imgName": "启示Apocalypto", "aka": "启示 Apocalypto", "rating": "8.7", "year": "2006", "done": "2023-05-25"}, {"title": "窃听风云2", "imgName": "窃听风云2", "aka": "窃听风云2", "rating": "7.6", "year": "2011", "done": "2023-05-20"}, {"title": "窃听风云 竊聽風雲", "imgName": "窃听风云竊聽風雲", "aka": "窃听风云 竊聽風雲", "rating": "7.6", "year": "2009", "done": "2023-05-20"}, {"title": "色，戒", "imgName": "色戒", "aka": "色，戒", "rating": "8.7", "year": "2007", "done": "2023-05-17"}, {"title": "保你平安", "imgName": "保你平安", "aka": "保你平安", "rating": "7.7", "year": "2022", "done": "2023-05-14"}, {"title": "画江湖之不良人6", "imgName": "画江湖之不良人6", "aka": "画江湖之不良人6", "rating": "9.5", "year": "2023", "done": "2023-05-11"}, {"title": "盟约 The Covenant", "imgName": "盟约TheCovenant", "aka": "盟约 The Covenant", "rating": "7.3", "year": "2023", "done": "2023-05-10"}, {"title": "宇宙探索编辑部", "imgName": "宇宙探索编辑部", "aka": "宇宙探索编辑部", "rating": "8.0", "year": "2021", "done": "2023-05-10"}, {"title": "香水 Perfume: The Story of a Murderer", "imgName": "香水PerfumeTheStoryofaMurderer", "aka": "香水 Perfume: The Story of a Murderer", "rating": "8.5", "year": "2006", "done": "2023-05-09"}, {"title": "银河护卫队3 Guardians of the Galaxy Vol. 3", "imgName": "银河护卫队3GuardiansoftheGalaxyVol3", "aka": "银河护卫队3 Guardians of the Galaxy Vol. 3", "rating": "8.4", "year": "2023", "done": "2023-05-05"}, {"title": "银河护卫队2 Guardians of the Galaxy Vol. 2", "imgName": "银河护卫队2GuardiansoftheGalaxyVol2", "aka": "银河护卫队2 Guardians of the Galaxy Vol. 2", "rating": "8.0", "year": "2017", "done": "2023-05-05"}, {"title": "银河护卫队 Guardians of the Galaxy", "imgName": "银河护卫队GuardiansoftheGalaxy", "aka": "银河护卫队 Guardians of the Galaxy", "rating": "8.1", "year": "2014", "done": "2023-05-05"}, {"title": "漫长的季节", "imgName": "漫长的季节", "aka": "漫长的季节", "rating": "9.4", "year": "2023", "done": "2023-05-05"}, {"title": "深海", "imgName": "深海", "aka": "深海", "rating": "7.2", "year": "2023", "done": "2023-05-02"}, {"title": "男孩、鼹鼠、狐狸和马 The Boy, the <PERSON>le, the Fox and the Horse", "imgName": "男孩鼹鼠狐狸和马TheBoytheMoletheFoxandtheHorse", "aka": "男孩、鼹鼠、狐狸和马 The Boy, the <PERSON>le, the Fox and the Horse", "rating": "8.3", "year": "2022", "done": "2023-04-18"}, {"title": "怒呛人生 Beef", "imgName": "怒呛人生Beef", "aka": "怒呛人生 Beef", "rating": "8.7", "year": "2023", "done": "2023-04-16"}, {"title": "无名", "imgName": "无名", "aka": "无名", "rating": "6.6", "year": "2023", "done": "2023-04-16"}, {"title": "流浪地球2", "imgName": "流浪地球2", "aka": "流浪地球2", "rating": "8.3", "year": "2023", "done": "2023-04-15"}, {"title": "俄罗斯方块 Tetris", "imgName": "俄罗斯方块Tetris", "aka": "俄罗斯方块 Tetris", "rating": "8.1", "year": "2023", "done": "2023-04-09"}, {"title": "走到尽头 끝까지 간다", "imgName": "走到尽头끝까지간다", "aka": "走到尽头 끝까지 간다", "rating": "8.0", "year": "2014", "done": "2023-04-09"}, {"title": "鲸 The Whale", "imgName": "鲸TheWhale", "aka": "鲸 The Whale", "rating": "8.0", "year": "2022", "done": "2023-03-27"}, {"title": "最后生还者 第一季 The Last of Us Season 1", "imgName": "最后生还者第一季TheLastofUsSeason1", "aka": "最后生还者 第一季 The Last of Us Season 1", "rating": "9.1", "year": "2023", "done": "2023-03-26"}, {"title": "黑暗荣耀 第二季 더 글로리2", "imgName": "黑暗荣耀第二季더글로리2", "aka": "黑暗荣耀 第二季 더 글로리2", "rating": "9.1", "year": "2023", "done": "2023-03-11"}, {"title": "1923 第一季 1923 Season 1", "imgName": "1923第一季1923Season1", "aka": "1923 第一季 1923 Season 1", "rating": "9.3", "year": "2022", "done": "2023-03-04"}, {"title": "千寻小姐 ちひろさん", "imgName": "千寻小姐ちひろさん", "aka": "千寻小姐 ちひろさん", "rating": "7.7", "year": "2023", "done": "2023-02-28"}, {"title": "中国奇谭", "imgName": "中国奇谭", "aka": "中国奇谭", "rating": "8.8", "year": "2023", "done": "2023-02-27"}, {"title": "目击者之追凶 目擊者", "imgName": "目击者之追凶目擊者", "aka": "目击者之追凶 目擊者", "rating": "8.2", "year": "2017", "done": "2023-02-26"}, {"title": "正义回廊 正義迴廊", "imgName": "正义回廊正義迴廊", "aka": "正义回廊 正義迴廊", "rating": "7.6", "year": "2022", "done": "2023-02-26"}, {"title": "安多 第一季 Andor Season 1", "imgName": "安多第一季AndorSeason1", "aka": "安多 第一季 Andor Season 1", "rating": "9.1", "year": "2022", "done": "2023-02-20"}, {"title": "蒙太奇 몽타주", "imgName": "蒙太奇몽타주", "aka": "蒙太奇 몽타주", "rating": "8.2", "year": "2013", "done": "2023-02-19"}, {"title": "十三条命 Thirteen Lives", "imgName": "十三条命ThirteenLives", "aka": "十三条命 Thirteen Lives", "rating": "8.5", "year": "2022", "done": "2023-02-14"}, {"title": "芙蓉镇", "imgName": "芙蓉镇", "aka": "芙蓉镇", "rating": "9.3", "year": "1987", "done": "2023-02-12"}, {"title": "巴比伦 Babylon", "imgName": "巴比伦Babylon", "aka": "巴比伦 Babylon", "rating": "8.0", "year": "2022", "done": "2023-02-12"}, {"title": "红高粱", "imgName": "红高粱", "aka": "红高粱", "rating": "8.5", "year": "1988", "done": "2023-02-12"}, {"title": "惠子，凝视 ケイコ 目を澄ませて", "imgName": "惠子凝视ケイコ目を澄ませて", "aka": "惠子，凝视 ケイコ 目を澄ませて", "rating": "7.9", "year": "2022", "done": "2023-02-11"}, {"title": "黑暗荣耀 더 글로리", "imgName": "黑暗荣耀더글로리", "aka": "黑暗荣耀 더 글로리", "rating": "8.9", "year": "2022", "done": "2023-02-04"}, {"title": "流人 第二季 Slow Horses Season 2", "imgName": "流人第二季SlowHorsesSeason2", "aka": "流人 第二季 Slow Horses Season 2", "rating": "9.0", "year": "2022", "done": "2023-02-02"}, {"title": "勇士 Warrior", "imgName": "勇士Warrior", "aka": "勇士 Warrior", "rating": "8.9", "year": "2011", "done": "2023-01-31"}, {"title": "间谍过家家 Part 2 SPY×FAMILY 第2クール", "imgName": "间谍过家家Part2SPY×FAMILY第2クール", "aka": "间谍过家家 Part 2 SPY×FAMILY 第2クール", "rating": "8.9", "year": "2022", "done": "2023-01-30"}, {"title": "塔尔萨之王 第一季 Tulsa King Season 1", "imgName": "塔尔萨之王第一季TulsaKingSeason1", "aka": "塔尔萨之王 第一季 Tulsa King Season 1", "rating": "8.5", "year": "2022", "done": "2023-01-28"}, {"title": "夜枭 올빼미", "imgName": "夜枭올빼미", "aka": "夜枭 올빼미", "rating": "7.1", "year": "2022", "done": "2023-01-22"}, {"title": "满江红", "imgName": "满江红", "aka": "满江红", "rating": "7.1", "year": "2023", "done": "2023-01-22"}, {"title": "流人 第一季 Slow Horses Season 1", "imgName": "流人第一季SlowHorsesSeason1", "aka": "流人 第一季 Slow Horses Season 1", "rating": "8.5", "year": "2022", "done": "2023-01-08"}, {"title": "解放黑奴 Emancipation", "imgName": "解放黑奴Emancipation", "aka": "解放黑奴 Emancipation", "rating": "7.3", "year": "2022", "done": "2023-01-01"}]