[{"title": "致命ID Identity", "imgName": "致命IDIdentity", "aka": "致命ID Identity", "rating": "8.9", "year": "2003", "done": "2017-12-30"}, {"title": "芳华", "imgName": "芳华", "aka": "芳华", "rating": "7.7", "year": "2017", "done": "2017-12-17"}, {"title": "帕丁顿熊 Paddington", "imgName": "帕丁顿熊Paddington", "aka": "帕丁顿熊 Paddington", "rating": "7.8", "year": "2014", "done": "2017-12-12"}, {"title": "帕丁顿熊2 Paddington 2", "imgName": "帕丁顿熊2Paddington2", "aka": "帕丁顿熊2 Paddington 2", "rating": "8.0", "year": "2017", "done": "2017-12-10"}, {"title": "底特律 Detroit", "imgName": "底特律Detroit", "aka": "底特律 Detroit", "rating": "7.8", "year": "2017", "done": "2017-12-09"}, {"title": "寻梦环游记 Coco", "imgName": "寻梦环游记Coco", "aka": "寻梦环游记 Coco", "rating": "9.1", "year": "2017", "done": "2017-11-25"}, {"title": "银翼杀手2049 Blade Runner 2049", "imgName": "银翼杀手2049BladeRunner2049", "aka": "银翼杀手2049 Blade Runner 2049", "rating": "8.3", "year": "2017", "done": "2017-11-11"}, {"title": "楚门的世界 The Truman Show", "imgName": "楚门的世界TheTrumanShow", "aka": "楚门的世界 The Truman Show", "rating": "9.4", "year": "1998", "done": "2017-10-16"}, {"title": "出租车司机", "imgName": "出租车司机", "aka": "出租车司机", "rating": "被封了", "year": "2017", "done": "2017-10-04"}, {"title": "羞羞的铁拳", "imgName": "羞羞的铁拳", "aka": "羞羞的铁拳", "rating": "6.8", "year": "2017", "done": "2017-10-01"}, {"title": "追龙 追龍", "imgName": "追龙追龍", "aka": "追龙 追龍", "rating": "7.2", "year": "2017", "done": "2017-10-01"}, {"title": "绝命毒师 第一季 Breaking Bad Season 1", "imgName": "绝命毒师第一季BreakingBadSeason1", "aka": "绝命毒师 第一季 Breaking Bad Season 1", "rating": "9.2", "year": "2008", "done": "2017-09-20"}, {"title": "蝙蝠侠：黑暗骑士 The Dark Knight", "imgName": "蝙蝠侠黑暗骑士TheDarkKnight", "aka": "蝙蝠侠：黑暗骑士 The Dark Knight", "rating": "9.2", "year": "2008", "done": "2017-09-17"}, {"title": "一念无明 一念無明", "imgName": "一念无明一念無明", "aka": "一念无明 一念無明", "rating": "7.9", "year": "2016", "done": "2017-07-30"}, {"title": "战狼2", "imgName": "战狼2", "aka": "战狼2", "rating": "7.1", "year": "2017", "done": "2017-07-17"}, {"title": "感化院 Freistatt", "imgName": "感化院Freistatt", "aka": "感化院 Freistatt", "rating": "8.3", "year": "2015", "done": "2017-07-16"}, {"title": "大护法", "imgName": "大护法", "aka": "大护法", "rating": "7.8", "year": "2017", "done": "2017-07-13"}, {"title": "地球上的星星 Taare Zameen Par", "imgName": "地球上的星星TaareZameenPar", "aka": "地球上的星星 Taare Zameen Par", "rating": "8.9", "year": "2007", "done": "2017-06-19"}, {"title": "西西里的美丽传说 Malèna", "imgName": "西西里的美丽传说Malèna", "aka": "西西里的美丽传说 Malèna", "rating": "8.9", "year": "2000", "done": "2017-06-11"}, {"title": "彗星来的那一夜 Coherence", "imgName": "彗星来的那一夜Coherence", "aka": "彗星来的那一夜 Coherence", "rating": "8.6", "year": "2013", "done": "2017-06-11"}, {"title": "加勒比海盗5：死无对证 Pirates of the Caribbean: Dead Men Tell No Tales", "imgName": "加勒比海盗5死无对证PiratesoftheCaribbeanDeadMenTellNoTales", "aka": "加勒比海盗5：死无对证 Pirates of the Caribbean: Dead Men Tell No Tales", "rating": "7.2", "year": "2017", "done": "2017-06-10"}, {"title": "神奇女侠 Wonder Woman", "imgName": "神奇女侠WonderWoman", "aka": "神奇女侠 Wonder Woman", "rating": "7.1", "year": "2017", "done": "2017-06-04"}, {"title": "猜火车2 T2 Trainspotting", "imgName": "猜火车2T2Trainspotting", "aka": "猜火车2 T2 Trainspotting", "rating": "8.0", "year": "2017", "done": "2017-05-30"}, {"title": "猜火车 Trainspotting", "imgName": "猜火车Trainspotting", "aka": "猜火车 Trainspotting", "rating": "8.6", "year": "1996", "done": "2017-05-28"}, {"title": "忠犬八公的故事 Hachi: A Dog's Tale", "imgName": "忠犬八公的故事HachiADogsTale", "aka": "忠犬八公的故事 Hachi: A Dog's Tale", "rating": "9.4", "year": "2009", "done": "2017-05-28"}, {"title": "宝贝老板 The Boss Baby", "imgName": "宝贝老板TheBossBaby", "aka": "宝贝老板 The Boss Baby", "rating": "7.7", "year": "2017", "done": "2017-05-21"}, {"title": "沃伦 Wołyń", "imgName": "沃伦Wołyń", "aka": "沃伦 Wołyń", "rating": "8.3", "year": "2016", "done": "2017-05-20"}, {"title": "天才少女 Gifted", "imgName": "天才少女Gifted", "aka": "天才少女 Gifted", "rating": "8.3", "year": "2017", "done": "2017-05-13"}, {"title": "摔跤吧！爸爸 Dangal", "imgName": "摔跤吧爸爸Dangal", "aka": "摔跤吧！爸爸 Dangal", "rating": "9.0", "year": "2016", "done": "2017-05-06"}, {"title": "银河护卫队2 Guardians of the Galaxy Vol. 2", "imgName": "银河护卫队2GuardiansoftheGalaxyVol2", "aka": "银河护卫队2 Guardians of the Galaxy Vol. 2", "rating": "8.0", "year": "2017", "done": "2017-05-06"}, {"title": "春娇救志明 春嬌救志明", "imgName": "春娇救志明春嬌救志明", "aka": "春娇救志明 春嬌救志明", "rating": "7.0", "year": "2017", "done": "2017-04-29"}, {"title": "速度与激情8 The Fate of the Furious", "imgName": "速度与激情8TheFateoftheFurious", "aka": "速度与激情8 The Fate of the Furious", "rating": "7.0", "year": "2017", "done": "2017-04-29"}, {"title": "斯隆女士 Miss Sloane", "imgName": "斯隆女士MissS<PERSON>ane", "aka": "斯隆女士 Miss Sloane", "rating": "8.8", "year": "2016", "done": "2017-04-04"}, {"title": "看不见的客人 Contratiempo", "imgName": "看不见的客人Contratiempo", "aka": "看不见的客人 Contratiempo", "rating": "8.8", "year": "2016", "done": "2017-03-28"}, {"title": "恐袭波士顿 Patriots Day", "imgName": "恐袭波士顿PatriotsDay", "aka": "恐袭波士顿 Patriots Day", "rating": "8.0", "year": "2016", "done": "2017-03-18"}, {"title": "最终幻想15：王者之剑 Kingsglaive Final Fantasy XV", "imgName": "最终幻想15王者之剑KingsglaiveFinalFantasyXV", "aka": "最终幻想15：王者之剑 Kingsglaive Final Fantasy XV", "rating": "7.2", "year": "2016", "done": "2017-03-15"}, {"title": "隐藏人物 Hidden Figures", "imgName": "隐藏人物HiddenFigures", "aka": "隐藏人物 Hidden Figures", "rating": "8.9", "year": "2016", "done": "2017-03-12"}, {"title": "一条狗的使命 A Dog's Purpose", "imgName": "一条狗的使命ADogsPurpose", "aka": "一条狗的使命 A Dog's Purpose", "rating": "7.7", "year": "2017", "done": "2017-03-09"}, {"title": "金刚狼3：殊死一战 Logan", "imgName": "金刚狼3殊死一战Logan", "aka": "金刚狼3：殊死一战 Logan", "rating": "8.3", "year": "2017", "done": "2017-03-05"}, {"title": "海边的曼彻斯特 Manchester by the Sea", "imgName": "海边的曼彻斯特ManchesterbytheSea", "aka": "海边的曼彻斯特 Manchester by the Sea", "rating": "8.6", "year": "2016", "done": "2017-03-05"}, {"title": "共助 공조", "imgName": "共助공조", "aka": "共助 공조", "rating": "7.3", "year": "2017", "done": "2017-03-03"}, {"title": "赴汤蹈火 Hell or High Water", "imgName": "赴汤蹈火HellorHighWater", "aka": "赴汤蹈火 Hell or High Water", "rating": "8.1", "year": "2016", "done": "2017-02-25"}, {"title": "欢乐好声音 Sing", "imgName": "欢乐好声音Sing", "aka": "欢乐好声音 Sing", "rating": "8.1", "year": "2016", "done": "2017-02-21"}, {"title": "王者 더 킹", "imgName": "王者더킹", "aka": "王者 더 킹", "rating": "7.8", "year": "2017", "done": "2017-02-21"}, {"title": "爆裂鼓手 Whiplash", "imgName": "爆裂鼓手Whiplash", "aka": "爆裂鼓手 Whiplash", "rating": "8.7", "year": "2014", "done": "2017-02-15"}, {"title": "爱乐之城 La La Land", "imgName": "爱乐之城LaLaLand", "aka": "爱乐之城 La La Land", "rating": "8.4", "year": "2016", "done": "2017-02-14"}, {"title": "乘风破浪", "imgName": "乘风破浪", "aka": "乘风破浪", "rating": "6.8", "year": "2017", "done": "2017-02-12"}, {"title": "废纸板拳击手 Cardboard Boxer", "imgName": "废纸板拳击手CardboardBoxer", "aka": "废纸板拳击手 Cardboard Boxer", "rating": "8.2", "year": "2016", "done": "2017-02-01"}, {"title": "闻香识女人 Scent of a Woman", "imgName": "闻香识女人ScentofaWoman", "aka": "闻香识女人 Scent of a Woman", "rating": "9.1", "year": "1992", "done": "2017-01-31"}, {"title": "教父 The Godfather", "imgName": "教父The<PERSON>odfather", "aka": "教父 The Godfather", "rating": "9.3", "year": "1972", "done": "2017-01-28"}, {"title": "教父2 The Godfather: Part II", "imgName": "教父2TheGodfatherPartII", "aka": "教父2 The Godfather: Part II", "rating": "9.3", "year": "1974", "done": "2017-01-28"}, {"title": "教父3 The Godfather: Part III", "imgName": "教父3TheGodfatherPartIII", "aka": "教父3 The Godfather: Part III", "rating": "9.0", "year": "1990", "done": "2017-01-28"}, {"title": "总有一天 Der kommer en dag", "imgName": "总有一天Derkommerendag", "aka": "总有一天 Der kommer en dag", "rating": "8.7", "year": "2016", "done": "2017-01-25"}, {"title": "萨利机长 Sully", "imgName": "萨利机长Sully", "aka": "萨利机长 Sully", "rating": "8.3", "year": "2016", "done": "2017-01-23"}, {"title": "神奇动物在哪里 Fantastic Beasts and Where to Find Them", "imgName": "神奇动物在哪里FantasticBeastsandWheretoFindThem", "aka": "神奇动物在哪里 Fantastic Beasts and Where to Find Them", "rating": "7.7", "year": "2016", "done": "2017-01-21"}]