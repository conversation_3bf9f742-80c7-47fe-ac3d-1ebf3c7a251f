[{"title": "老千 타짜", "imgName": "老千타짜", "aka": "老千 타짜", "rating": "7.6", "year": "2006", "done": "2020-12-06"}, {"title": "局内人 내부자들", "imgName": "局内人내부자들", "aka": "局内人 내부자들", "rating": "8.0", "year": "2015", "done": "2020-12-05"}, {"title": "致命魔术 The Prestige", "imgName": "致命魔术ThePrestige", "aka": "致命魔术 The Prestige", "rating": "8.9", "year": "2006", "done": "2020-12-05"}, {"title": "电话 콜", "imgName": "电话콜", "aka": "电话 콜", "rating": "7.6", "year": "2020", "done": "2020-12-04"}, {"title": "一一", "imgName": "一一", "aka": "一一", "rating": "9.1", "year": "2000", "done": "2020-11-29"}, {"title": "半泽直树2 半沢直樹2", "imgName": "半泽直树2半沢直樹2", "aka": "半泽直树2 半沢直樹2", "rating": "9.4", "year": "2020", "done": "2020-11-15"}, {"title": "后翼弃兵 The Queen's Gambit", "imgName": "后翼弃兵TheQueensGambit", "aka": "后翼弃兵 The Queen's Gambit", "rating": "9.0", "year": "2020", "done": "2020-11-14"}, {"title": "老无所依 No Country for Old Men", "imgName": "老无所依NoCountryforOldMen", "aka": "老无所依 No Country for Old Men", "rating": "8.3", "year": "2007", "done": "2020-10-31"}, {"title": "姜子牙", "imgName": "姜子牙", "aka": "姜子牙", "rating": "6.7", "year": "2020", "done": "2020-10-01"}, {"title": "我和我的家乡", "imgName": "我和我的家乡", "aka": "我和我的家乡", "rating": "7.0", "year": "2020", "done": "2020-10-01"}, {"title": "从邪恶中拯救我 다만 악에서 구하소서", "imgName": "从邪恶中拯救我다만악에서구하소서", "aka": "从邪恶中拯救我 다만 악에서 구하소서", "rating": "7.3", "year": "2020", "done": "2020-09-11"}, {"title": "信条 Tenet", "imgName": "信条Tenet", "aka": "信条 Tenet", "rating": "7.6", "year": "2020", "done": "2020-09-04"}, {"title": "聚焦 Spotlight", "imgName": "聚焦Spotlight", "aka": "聚焦 Spotlight", "rating": "8.8", "year": "2015", "done": "2020-08-23"}, {"title": "1/2的魔法 Onward", "imgName": "12的魔法Onward", "aka": "1/2的魔法 Onward", "rating": "7.6", "year": "2020", "done": "2020-08-20"}, {"title": "乔乔的异想世界 Jojo Rabbit", "imgName": "乔乔的异想世界JojoRabbit", "aka": "乔乔的异想世界 Jojo Rabbit", "rating": "8.3", "year": "2019", "done": "2020-08-18"}, {"title": "雾山五行", "imgName": "雾山五行", "aka": "雾山五行", "rating": "8.8", "year": "2020", "done": "2020-08-16"}, {"title": "1917", "imgName": "1917", "aka": "1917", "rating": "8.5", "year": "2019", "done": "2020-08-16"}, {"title": "八佰", "imgName": "八佰", "aka": "八佰", "rating": "7.5", "year": "2020", "done": "2020-08-15"}, {"title": "枪火 鎗火", "imgName": "枪火鎗火", "aka": "枪火 鎗火", "rating": "8.8", "year": "1999", "done": "2020-08-08"}, {"title": "跛豪", "imgName": "跛豪", "aka": "跛豪", "rating": "8.3", "year": "1991", "done": "2020-08-07"}, {"title": "放逐 P<PERSON>nu sanatorija", "imgName": "放逐Pelnusanatorija", "aka": "放逐 P<PERSON>nu sanatorija", "rating": "7.3", "year": "2016", "done": "2020-08-04"}, {"title": "暗战 暗戰", "imgName": "暗战暗戰", "aka": "暗战 暗戰", "rating": "8.5", "year": "1999", "done": "2020-08-05"}, {"title": "黑社会 黑社會", "imgName": "黑社会黑社會", "aka": "黑社会 黑社會", "rating": "8.3", "year": "2005", "done": "2020-08-02"}, {"title": "无间道2 無間道II", "imgName": "无间道2無間道II", "aka": "无间道2 無間道II", "rating": "8.7", "year": "2003", "done": "2020-07-20"}, {"title": "无间道3：终极无间 無間道III 終極無間", "imgName": "无间道3终极无间無間道III終極無間", "aka": "无间道3：终极无间 無間道III 終極無間", "rating": "8.1", "year": "2003", "done": "2020-07-20"}, {"title": "灰猎犬号 Greyhound", "imgName": "灰猎犬号Greyhound", "aka": "灰猎犬号 Greyhound", "rating": "7.8", "year": "2020", "done": "2020-07-12"}, {"title": "半泽直树 半沢直樹", "imgName": "半泽直树半沢直樹", "aka": "半泽直树 半沢直樹", "rating": "9.2", "year": "2013", "done": "2020-06-27"}, {"title": "隐秘的角落", "imgName": "隐秘的角落", "aka": "隐秘的角落", "rating": "8.8", "year": "2020", "done": "2020-06-26"}, {"title": "穿条纹睡衣的男孩 The Boy in the Striped Pajamas", "imgName": "穿条纹睡衣的男孩TheBoyintheStripedPajamas", "aka": "穿条纹睡衣的男孩 The Boy in the Striped Pajamas", "rating": "9.1", "year": "2008", "done": "2020-0606"}, {"title": "狩猎 J<PERSON>ten", "imgName": "狩猎J<PERSON>ten", "aka": "狩猎 J<PERSON>ten", "rating": "9.1", "year": "2012", "done": "2020-06-06"}, {"title": "好莱坞往事 Once Upon a Time... in Hollywood", "imgName": "好莱坞往事OnceUponaTimeinHollywood", "aka": "好莱坞往事 Once Upon a Time... in Hollywood", "rating": "7.3", "year": "2019", "done": "2020-06-05"}, {"title": "飞越疯人院 One Flew Over the Cuckoo's Nest", "imgName": "飞越疯人院OneFlewOvertheCuckoosNest", "aka": "飞越疯人院 One Flew Over the Cuckoo's Nest", "rating": "9.1", "year": "1975", "done": "2020-05-17"}, {"title": "缘分天注定 Serendipity", "imgName": "缘分天注定Serendipity", "aka": "缘分天注定 Serendipity", "rating": "7.6", "year": "2001", "done": "2020-04-18"}, {"title": "星际穿越 Interstellar", "imgName": "星际穿越Interstellar", "aka": "星际穿越 Interstellar", "rating": "9.4", "year": "2014", "done": "2020-04-12"}, {"title": "零零零 第一季 ZeroZeroZero Season 1", "imgName": "零零零第一季ZeroZeroZeroSeason1", "aka": "零零零 第一季 ZeroZeroZero Season 1", "rating": "9.1", "year": "2019", "done": "2020-04-12"}, {"title": "达拉斯买家俱乐部 Dallas Buyers Club", "imgName": "达拉斯买家俱乐部DallasBuyersClub", "aka": "达拉斯买家俱乐部 Dallas Buyers Club", "rating": "8.8", "year": "2013", "done": "2020-03-29"}, {"title": "饥饿站台 El hoyo", "imgName": "饥饿站台Elhoyo", "aka": "饥饿站台 El hoyo", "rating": "7.8", "year": "2019", "done": "2020-03-29"}, {"title": "绅士们 The Gentlemen", "imgName": "绅士们The<PERSON><PERSON><PERSON>en", "aka": "绅士们 The Gentlemen", "rating": "8.3", "year": "2019", "done": "2020-03-28"}, {"title": "误杀", "imgName": "误杀", "aka": "误杀", "rating": "7.5", "year": "2019", "done": "2020-03-21"}, {"title": "利刃出鞘 Knives Out", "imgName": "利刃出鞘KnivesOut", "aka": "利刃出鞘 Knives Out", "rating": "8.1", "year": "2019", "done": "2020-03-15"}, {"title": "极速车王 Ford v Ferrari", "imgName": "极速车王Ford<PERSON>Fer<PERSON>i", "aka": "极速车王 Ford v Ferrari", "rating": "8.5", "year": "2019", "done": "2020-03-11"}, {"title": "平原上的夏洛克", "imgName": "平原上的夏洛克", "aka": "平原上的夏洛克", "rating": "7.8", "year": "2019", "done": "2020-02-29"}, {"title": "南山的部长们 남산의 부장들", "imgName": "南山的部长们남산의부장들", "aka": "南山的部长们 남산의 부장들", "rating": "8.1", "year": "2020", "done": "2020-02-22"}, {"title": "阳光普照 陽光普照", "imgName": "阳光普照陽光普照", "aka": "阳光普照 陽光普照", "rating": "8.5", "year": "2019", "done": "2020-02-10"}, {"title": "叶问4：完结篇 葉問4：完結篇", "imgName": "叶问4完结篇葉問4完結篇", "aka": "叶问4：完结篇 葉問4：完結篇", "rating": "6.8", "year": "2019", "done": "2020-02-09"}, {"title": "曼达洛人 第一季 The Mandalorian Season 1", "imgName": "曼达洛人第一季TheMandalorianSeason1", "aka": "曼达洛人 第一季 The Mandalorian Season 1", "rating": "9.2", "year": "2019", "done": "2020-01-27"}, {"title": "黑皮书 Zwartboek", "imgName": "黑皮书Zwartboek", "aka": "黑皮书 Zwartboek", "rating": "8.4", "year": "2006", "done": "2020-10-30"}, {"title": "无间道 無間道", "imgName": "无间道無間道", "aka": "无间道 無間道", "rating": "9.3", "year": "2002", "done": "2020-07-20"}, {"title": "机智医生生活 슬기로운 의사생활", "imgName": "机智医生生活슬기로운의사생활", "aka": "机智医生生活 슬기로운 의사생활", "rating": "9.5", "year": "2020", "done": "2020-05-22"}, {"title": "囚徒 Prisoners", "imgName": "囚徒Prisoners", "aka": "囚徒 Prisoners", "rating": "8.1", "year": "2013", "done": "2020-07-15"}, {"title": "布达佩斯大饭店 The Grand Budapest Hotel", "imgName": "布达佩斯大饭店TheGrandBudapestHotel", "aka": "布达佩斯大饭店 The Grand Budapest Hotel", "rating": "8.9", "year": "2014", "done": "2020-05-24"}, {"title": "风骚律师 第五季 Better Call Saul Season 5", "imgName": "风骚律师第五季BetterCallSaulSeason5", "aka": "风骚律师 第五季 Better Call Saul Season 5", "rating": "9.7", "year": "2020", "done": "2020-05-01"}, {"title": "多哥 Togo", "imgName": "多哥Togo", "aka": "多哥 Togo", "rating": "8.8", "year": "2019", "done": "2020-04-19"}, {"title": "山河故人", "imgName": "山河故人", "aka": "山河故人", "rating": "8.0", "year": "2015", "done": "2020-04-18"}, {"title": "真探 第一季 True Detective Season 1", "imgName": "真探第一季TrueDetectiveSeason1", "aka": "真探 第一季 True Detective Season 1", "rating": "9.3", "year": "2014", "done": "2020-04-04"}, {"title": "阿拉丁 <PERSON><PERSON><PERSON>", "imgName": "阿拉丁<PERSON><PERSON><PERSON>", "aka": "阿拉丁 <PERSON><PERSON><PERSON>", "rating": "7.4", "year": "2019", "done": "2020-03-15"}, {"title": "王国 第二季 킹덤 Season 2", "imgName": "王国第二季킹덤Season2", "aka": "王国 第二季 킹덤 Season 2", "rating": "8.3", "year": "2020", "done": "2020-03-14"}, {"title": "南方车站的聚会", "imgName": "南方车站的聚会", "aka": "南方车站的聚会", "rating": "7.4", "year": "2019", "done": "2020-02-23"}, {"title": "毒枭：墨西哥 第二季 Narcos: Mexico Season 2", "imgName": "毒枭墨西哥第二季NarcosMexicoSeason2", "aka": "毒枭：墨西哥 第二季 Narcos: Mexico Season 2", "rating": "9.0", "year": "2020", "done": "2020-02-17"}, {"title": "王国 第一季 킹덤 Season 1", "imgName": "王国第一季킹덤Season1", "aka": "王国 第一季 킹덤 Season 1", "rating": "8.6", "year": "2019", "done": "2020-02-11"}, {"title": "阳光普照 陽光普照", "imgName": "阳光普照陽光普照", "aka": "阳光普照 陽光普照", "rating": "8.5", "year": "2019", "done": "2020-02-10"}, {"title": "美利坚女士 Taylor Swift: Miss Americana", "imgName": "美利坚女士TaylorSwiftMissAmericana", "aka": "美利坚女士 Taylor Swift: Miss Americana", "rating": "8.7", "year": "2020", "done": "2020-02-10"}, {"title": "82年生的金智英 82년생 김지영", "imgName": "82年生的金智英82년생김지영", "aka": "82年生的金智英 82년생 김지영", "rating": "8.6", "year": "2019", "done": "2020-02-02"}, {"title": "去他*的世界 第一季 The End of the F***ing World Season 1", "imgName": "去他的世界第一季TheEndoftheFingWorldSeason1", "aka": "去他*的世界 第一季 The End of the F***ing World Season 1", "rating": "9.1", "year": "2017", "done": "2020-01-27"}]