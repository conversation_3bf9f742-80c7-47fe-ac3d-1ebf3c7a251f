[{"title": "嗜血法医 第八季 Dexter Season 8", "aka": "嗜血法医 第八季 Dexter Season 8", "rating": "8.8", "year": "2013", "done": "2022-01-06"}, {"title": "嗜血法医 第七季 Dexter Season 7", "aka": "嗜血法医 第七季 Dexter Season 7", "rating": "8.9", "year": "2012", "done": "2022-01-04"}, {"title": "嗜血法医 第六季 Dexter Season 6", "aka": "嗜血法医 第六季 Dexter Season 6", "rating": "9.0", "year": "2011", "done": "2022-01-03"}, {"title": "嗜血法医 第五季 Dexter Season 5", "aka": "嗜血法医 第五季 Dexter Season 5", "rating": "8.9", "year": "2010", "done": "2022-01-02"}, {"title": "嗜血法医 第四季 Dexter Season 4", "aka": "嗜血法医 第四季 Dexter Season 4", "rating": "9.0", "year": "2009", "done": "2022-01-01"}, {"title": "长津湖", "imgName": "长津湖", "aka": "长津湖", "rating": "7.4", "year": "2021", "done": "2022-01-10"}, {"title": "芝加哥七君子审判 The Trial of the Chicago 7", "imgName": "芝加哥七君子审判TheTrialoftheChicago7", "aka": "芝加哥七君子审判 The Trial of the Chicago 7", "rating": "8.6", "year": "2020", "done": "2022-01-08"}, {"title": "难以置信 Unbelievable", "imgName": "难以置信Unbelievable", "aka": "难以置信 Unbelievable", "rating": "9.2", "year": "2019", "done": "2022-01-09"}, {"title": "黄石 第四季 Yellowstone Season 4", "imgName": "黄石第四季YellowstoneSeason4", "aka": "黄石 第四季 Yellowstone Season 4", "rating": "9.1", "year": "2021", "done": "2022-01-08"}, {"title": "嗜血法医：杀魔新生 Dexter: New Blood", "imgName": "嗜血法医杀魔新生DexterNewBlood", "aka": "嗜血法医：杀魔新生 Dexter: New Blood", "rating": "8.6", "year": "2021", "done": "2022-01-11"}, {"title": "继承之战 第一季 Succession Season 1", "imgName": "继承之战第一季SuccessionSeason1", "aka": "继承之战 第一季 Succession Season 1", "rating": "8.3", "year": "2018", "done": "2022-01-14"}, {"title": "犬之力 The Power of the Dog", "imgName": "犬之力ThePoweroftheDog", "aka": "犬之力 The Power of the Dog", "rating": "7.7", "year": "2021", "done": "2022-01-17"}, {"title": "继承之战 第三季 Succession Season 3", "imgName": "继承之战第三季SuccessionSeason3", "aka": "继承之战 第三季 Succession Season 3", "rating": "9.3", "year": "2021", "done": "2022-01-16"}, {"title": "继承之战 第二季 Succession Season 2", "imgName": "继承之战第二季SuccessionSeason2", "aka": "继承之战 第二季 Succession Season 2", "rating": "9.3", "year": "2019", "done": "2022-01-15"}, {"title": "黑水 Dark Waters", "imgName": "黑水DarkWaters", "aka": "黑水 Dark Waters", "rating": "8.6", "year": "2019", "done": "2022-01-18"}, {"title": "兄弟连 Band of Brothers", "imgName": "兄弟连BandofBrothers", "aka": "兄弟连 Band of Brothers", "rating": "9.6", "year": "2001", "done": "2022-01-23"}, {"title": "奇巧计程车 ODD TAXI", "imgName": "奇巧计程车ODDTAXI", "aka": "奇巧计程车 ODD TAXI", "rating": "9.5", "year": "2021", "done": "2022-01-24"}, {"title": "杀死比尔 Kill Bill: Vol. 1", "imgName": "杀死比尔KillBillVol1", "aka": "杀死比尔 Kill Bill: Vol. 1", "rating": "8.3", "year": "2003", "done": "2022-01-25"}, {"title": "杀死比尔2 Kill Bill: Vol. 2", "imgName": "杀死比尔2KillBillVol2", "aka": "杀死比尔2 Kill Bill: Vol. 2", "rating": "8.1", "year": "2004", "done": "2022-01-26"}, {"title": "雄狮少年", "imgName": "雄狮少年", "aka": "雄狮少年", "rating": "8.3", "year": "2021", "done": "2022-02-09"}, {"title": "奇迹·笨小孩", "imgName": "奇迹·笨小孩", "aka": "奇迹·笨小孩", "rating": "7.4", "year": "2022", "done": "2022-02-07"}, {"title": "这个杀手不太冷静", "imgName": "这个杀手不太冷静", "aka": "这个杀手不太冷静", "rating": "6.6", "year": "2022", "done": "2022-02-04"}, {"title": "长津湖之水门桥", "imgName": "长津湖之水门桥", "aka": "长津湖之水门桥", "rating": "7.2", "year": "2022", "done": "2022-02-03"}, {"title": "狙击手", "imgName": "狙击手", "aka": "狙击手", "rating": "7.7", "year": "2022", "done": "2022-02-01"}, {"title": "四海", "imgName": "四海", "aka": "四海", "rating": "5.5", "year": "2022", "done": "2022-02-01"}, {"title": "僵尸校园 지금 우리 학교는", "imgName": "僵尸校园지금우리학교는", "aka": "僵尸校园 지금 우리 학교는", "rating": "6.0", "year": "2022", "done": "2022-01-29"}, {"title": "爱情神话", "imgName": "爱情神话", "aka": "爱情神话", "rating": "8.1", "year": "2021", "done": "2022-02-10"}, {"title": "波巴·费特之书 The Book of Boba <PERSON>tt", "imgName": "波巴·费特之书TheBookofBobaFett", "aka": "波巴·费特之书 The Book of Boba <PERSON>tt", "rating": "8.8", "year": "2021", "done": "2022-02-11"}, {"title": "驾驶我的车 ドライブ・マイ・カー", "imgName": "驾驶我的车ドライブ・マイ・カー", "aka": "驾驶我的车 ドライブ・マイ・カー", "rating": "7.9", "year": "2021", "done": "2022-04-13"}, {"title": "蜘蛛侠：英雄无归 Spider-Man: No Way Home", "imgName": "蜘蛛侠英雄无归Spider-ManNoWayHome", "aka": "蜘蛛侠：英雄无归 Spider-Man: No Way Home", "rating": "6.7", "year": "2021", "done": "2022-03-28"}, {"title": "疼痛难免 This Is Going to Hurt", "imgName": "疼痛难免ThisIsGoingtoHurt", "aka": "疼痛难免 This Is Going to Hurt", "rating": "9.4", "year": "2022", "done": "2022-03-27"}, {"title": "拆弹专家2", "imgName": "拆弹专家2", "aka": "拆弹专家2", "rating": "7.5", "year": "2020", "done": "2022-03-24"}, {"title": "青春变形记 Turning Red", "imgName": "青春变形记TurningRed", "aka": "青春变形记 Turning Red", "rating": "8.2", "year": "2022", "done": "2022-03-13"}, {"title": "逃亡 Flugt", "imgName": "逃亡Flugt", "aka": "逃亡 Flugt", "rating": "8.2", "year": "2021", "done": "2022-03-09"}, {"title": "扬名立万", "imgName": "扬名立万", "aka": "扬名立万", "rating": "7.4", "year": "2021", "done": "2022-02-24"}, {"title": "黑袍纠察队 第二季 The Boys Season 2", "imgName": "黑袍纠察队第二季TheBoysSeason2", "aka": "黑袍纠察队 第二季 The Boys Season 2", "rating": "8.6", "year": "2020", "done": "2022-02-20"}, {"title": "黑袍纠察队 第一季 The Boys Season 1", "imgName": "黑袍纠察队第一季TheBoysSeason1", "aka": "黑袍纠察队 第一季 The Boys Season 1", "rating": "8.6", "year": "2019", "done": "2022-02-19"}, {"title": "维京传奇：英灵神殿 第一季 Vikings: Valhalla Season 1", "imgName": "维京传奇英灵神殿第一季VikingsValhallaSeason1", "aka": "维京传奇：英灵神殿 第一季 Vikings: Valhalla Season 1", "rating": "7.9", "year": "2022", "done": "2022-02-27"}, {"title": "黑袍纠察队：劣迹 The Boys Presents: Diabolical", "imgName": "黑袍纠察队劣迹TheBoysPresentsDiabolical", "aka": "黑袍纠察队：劣迹 The Boys Presents: Diabolical", "rating": "7.7", "year": "2022", "done": "2022-03-09"}, {"title": "新蝙蝠侠 The Batman", "imgName": "新蝙蝠侠TheBatman", "aka": "新蝙蝠侠 The Batman", "rating": "7.5", "year": "2022", "done": "2022-04-21"}, {"title": "浴血黑帮 第六季 Peaky Blinders Season 6", "imgName": "浴血黑帮第六季PeakyBlindersSeason6", "aka": "浴血黑帮 第六季 Peaky Blinders Season 6", "rating": "9.4", "year": "2022", "done": "2022-04-17"}, {"title": "树大招风", "imgName": "树大招风", "aka": "樹大招風", "rating": "8.1", "year": "2016", "done": "2022-04-15"}, {"title": "套装 The Outfit", "imgName": "套装TheOutfit", "aka": "套装 The Outfit", "rating": "7.9", "year": "2022", "done": "2022-04-14"}, {"title": "人生切割术 第一季 Severance Season 1", "imgName": "人生切割术第一季SeveranceSeason1", "aka": "人生切割术 第一季 Severance Season 1", "rating": "9.1", "year": "2022", "done": "2022-04-23"}, {"title": "锅匠，裁缝，士兵，间谍 Tinker Tailor Soldier Spy", "imgName": "锅匠裁缝士兵间谍TinkerTailorSoldierSpy", "aka": "锅匠，裁缝，士兵，间谍 Tinker Tailor Soldier Spy", "rating": "7.9", "year": "2011", "done": "2022-04-25"}, {"title": "画江湖之不良人5", "imgName": "画江湖之不良人5", "aka": "画江湖之不良人5", "rating": "8.4", "year": "2022", "done": "2022-05-01"}, {"title": "1883", "imgName": "1883", "aka": "1883", "rating": "9.2", "year": "2021", "done": "2022-05-01"}, {"title": "手卷烟 手捲煙", "imgName": "手卷烟手捲煙", "aka": "手卷烟 手捲煙", "rating": "7.1", "year": "2020", "done": "2022-05-04"}, {"title": "机动部队 PTU", "imgName": "机动部队PTU", "aka": "机动部队 PTU", "rating": "8.1", "year": "2003", "done": "2022-05-02"}, {"title": "无神 Godless", "imgName": "无神Godless", "aka": "无神 Godless", "rating": "8.8", "year": "2017", "done": "2022-05-02"}, {"title": "和平使者 第一季 Peacemaker Season 1", "imgName": "和平使者第一季PeacemakerSeason1", "aka": "和平使者 第一季 Peacemaker Season 1", "rating": "8.8", "year": "2022", "done": "2022-05-08"}, {"title": "回到太空 Return to Space", "imgName": "回到太空ReturntoSpace", "aka": "回到太空 Return to Space", "rating": "8.7", "year": "2022", "done": "2022-05-05"}, {"title": "坏蛋联盟 The Bad Guys", "imgName": "坏蛋联盟TheBadGuys", "aka": "坏蛋联盟 The Bad Guys", "rating": "7.4", "year": "2022", "done": "2022-05-05"}, {"title": "月光骑士 Moon Knight", "imgName": "月光骑士MoonKnight", "aka": "月光骑士 Moon Knight", "rating": "8.2", "year": "2022", "done": "2022-05-04"}, {"title": "燃情岁月 Legends of the Fall", "imgName": "燃情岁月LegendsoftheFall", "aka": "燃情岁月 Legends of the Fall", "rating": "8.8", "year": "1994", "done": "2022-05-16"}, {"title": "国王的演讲 The King's Speech", "imgName": "国王的演讲TheKingsSpeech", "aka": "国王的演讲 The King's Speech", "rating": "8.4", "year": "2010", "done": "2022-05-15"}, {"title": "平家物语 平家物語", "imgName": "平家物语平家物語", "aka": "平家物语 平家物語", "rating": "8.8", "year": "2021", "done": "2022-05-15"}, {"title": "悲惨世界 Les Misérables", "imgName": "悲惨世界LesMisérables", "aka": "悲惨世界 Les Misérables", "rating": "8.6", "year": "2012", "done": "2022-05-12"}, {"title": "记忆碎片 Memento", "imgName": "记忆碎片Memento", "aka": "记忆碎片 Memento", "rating": "8.7", "year": "2000", "done": "2022-05-11"}, {"title": "追随 Following", "imgName": "追随Following", "aka": "追随 Following", "rating": "8.9", "year": "1998", "done": "2022-05-09"}, {"title": "一级恐惧 Primal Fear", "imgName": "一级恐惧PrimalFear", "aka": "一级恐惧 Primal Fear", "rating": "8.5", "year": "1996", "done": "2022-05-08"}, {"title": "洛城机密 L.A. Confidential", "imgName": "洛城机密LAConfidential", "aka": "洛城机密 L.A. Confidential", "rating": "8.8", "year": "1997", "done": "2022-05-22"}, {"title": "泰坦尼克号 Titanic", "imgName": "泰坦尼克号Titanic", "aka": "泰坦尼克号 Titanic", "rating": "9.4", "year": "1997", "done": "2022-05-22"}, {"title": "云图 Cloud Atlas", "imgName": "云图CloudAtlas", "aka": "云图 Cloud Atlas", "rating": "8.1", "year": "2012", "done": "2022-05-21"}, {"title": "爱，死亡和机器人 第三季 Love, Death & Robots Season 3", "imgName": "爱死亡和机器人第三季LoveDeathRobotsSeason3", "aka": "爱，死亡和机器人 第三季 Love, Death & Robots Season 3", "rating": "8.6", "year": "2022", "done": "2022-05-20"}, {"title": "荒蛮故事 Relatos salvajes", "imgName": "荒蛮故事Relatossalvajes", "aka": "荒蛮故事 Relatos salvajes", "rating": "8.8", "year": "2014", "done": "2022-05-19"}, {"title": "爱，死亡和机器人 第二季 Love, Death & Robots Season 2", "imgName": "爱死亡和机器人第二季LoveDeathRobotsSeason2", "aka": "爱，死亡和机器人 第二季 Love, Death & Robots Season 2", "rating": "6.8", "year": "2021", "done": "2022-05-19"}, {"title": "瞬息全宇宙 Everything Everywhere All at Once", "imgName": "瞬息全宇宙EverythingEverywhereAllatOnce", "aka": "瞬息全宇宙 Everything Everywhere All at Once", "rating": "7.9", "year": "2022", "done": "2022-05-18"}, {"title": "辛德勒的名单 Schindler's List", "imgName": "辛德勒的名单SchindlersList", "aka": "辛德勒的名单 Schindler's List", "rating": "9.6", "year": "1993", "done": "2022-05-17"}, {"title": "控方证人 Witness for the Prosecution", "imgName": "控方证人WitnessfortheProsecution", "aka": "控方证人 Witness for the Prosecution", "rating": "9.6", "year": "1957", "done": "2022-05-22"}, {"title": "非常嫌疑犯 The Usual Suspects", "imgName": "非常嫌疑犯TheUsualSuspects", "aka": "非常嫌疑犯 The Usual Suspects", "rating": "8.6", "year": "1995", "done": "2022-05-22"}, {"title": "国王排名 王様ランキング", "imgName": "国王排名王様ランキング", "aka": "国王排名 王様ランキング", "rating": "8.2", "year": "2021", "done": "2022-05-25"}, {"title": "乱世佳人 Gone with the Wind", "imgName": "乱世佳人GonewiththeWind", "aka": "乱世佳人 Gone with the Wind", "rating": "9.3", "year": "1939", "done": "2022-05-25"}, {"title": "触不可及 Intouchables", "imgName": "触不可及Intouchables", "aka": "触不可及 Intouchables", "rating": "9.3", "year": "2011", "done": "2022-05-24"}, {"title": "穆赫兰<PERSON> Mulholland Dr.", "imgName": "穆赫兰道MulhollandDr", "aka": "穆赫兰<PERSON> Mulholland Dr.", "rating": "8.4", "year": "2001", "done": "2022-05-23"}, {"title": "末代皇帝 The Last Emperor", "imgName": "末代皇帝TheLastEmperor", "aka": "末代皇帝 The Last Emperor", "rating": "9.3", "year": "1987", "done": "2022-05-30"}, {"title": "西部风云 Into the West", "imgName": "西部风云IntotheWest", "aka": "西部风云 Into the West", "rating": "9.1", "year": "2005", "done": "2022-05-29"}, {"title": "罗马假日 Roman Holiday", "imgName": "罗马假日RomanHoliday", "aka": "罗马假日 Roman Holiday", "rating": "9.1", "year": "1953", "done": "2022-05-26"}, {"title": "奇异博士2：疯狂多元宇宙 Doctor Strange in the Multiverse of Madness", "imgName": "奇异博士2疯狂多元宇宙DoctorStrangeintheMultiverseofMadness", "aka": "奇异博士2：疯狂多元宇宙 Doctor Strange in the Multiverse of Madness", "rating": "6.3", "year": "2022", "done": "2022-06-25"}, {"title": "天才不能承受之重 The Unbearable Weight of Massive Talent", "imgName": "天才不能承受之重TheUnbearableWeightofMassiveTalent", "aka": "天才不能承受之重 The Unbearable Weight of Massive Talent", "rating": "6.9", "year": "2022", "done": "2022-06-07"}, {"title": "我的解放日志 나의 해방일지", "imgName": "我的解放日志나의해방일지", "aka": "我的解放日志 나의 해방일지", "rating": "9.1", "year": "2022", "done": "2022-06-05"}, {"title": "了不起的盖茨比 The Great Gatsby", "imgName": "了不起的盖茨比TheGreatGatsby", "aka": "了不起的盖茨比 The Great Gatsby", "rating": "7.9", "year": "2013", "done": "2022-06-04"}, {"title": "智齿 智齒", "imgName": "智齿智齒", "aka": "智齿 智齒", "rating": "7.2", "year": "2021", "done": "2022-07-20"}, {"title": "间谍过家家 SPY×FAMILY", "imgName": "间谍过家家SPY×FAMILY", "aka": "间谍过家家 SPY×FAMILY", "rating": "9.0", "year": "2022", "done": "2022-07-18"}, {"title": "冬冬的假期", "imgName": "冬冬的假期", "aka": "冬冬的假期", "rating": "8.7", "year": "1984", "done": "2022-07-16"}, {"title": "黑袍纠察队 第三季 The Boys Season 3", "imgName": "黑袍纠察队第三季TheBoysSeason3", "aka": "黑袍纠察队 第三季 The Boys Season 3", "rating": "8.8", "year": "2022", "done": "2022-07-14"}, {"title": "纸牌屋 第一季 House of Cards Season 1", "imgName": "纸牌屋第一季HouseofCardsSeason1", "aka": "纸牌屋 第一季 House of Cards Season 1", "rating": "9.3", "year": "2013", "done": "2022-06-26"}, {"title": "目中无人", "imgName": "目中无人", "aka": "目中无人", "rating": "7.1", "year": "2022", "done": "2022-06-25"}, {"title": "剑客卡南 Karnan", "imgName": "剑客卡南Karnan", "aka": "剑客卡南 Karnan", "rating": "7.9", "year": "2021", "done": "2022-07-25"}, {"title": "非常律师禹英禑 이상한 변호사 우영우", "imgName": "非常律师禹英禑이상한변호사우영우", "aka": "非常律师禹英禑 이상한 변호사 우영우", "rating": "9.3", "year": "2022", "done": "2022-07-24"}, {"title": "白日焰火", "imgName": "白日焰火", "aka": "白日焰火", "rating": "7.6", "year": "2014", "done": "2022-07-24"}, {"title": "犯罪都市2 범죄도시2", "imgName": "犯罪都市2범죄도시2", "aka": "犯罪都市2 범죄도시2", "rating": "7.6", "year": "2022", "done": "2022-07-23"}, {"title": "不要抬头 Don't Look Up", "imgName": "不要抬头DontLookUp", "aka": "不要抬头 Don't Look Up", "rating": "7.6", "year": "2021", "done": "2022-07-23"}, {"title": "纸牌屋 第二季 House of Cards Season 2", "imgName": "纸牌屋第二季HouseofCardsSeason2", "aka": "纸牌屋 第二季 House of Cards Season 2", "rating": "9.2", "year": "2014", "done": "2022-07-23"}, {"title": "大白！ Baymax!", "imgName": "大白<PERSON>max", "aka": "大白！ Baymax!", "rating": "8.2", "year": "2022", "done": "2022-07-20"}, {"title": "非常杀手 더 킬러: 죽어도 되는 아이", "imgName": "非常杀手더킬러죽어도되는아이", "aka": "非常杀手 더 킬러: 죽어도 되는 아이", "rating": "6.3", "year": "2022", "done": "2022-07-30"}, {"title": "角斗士 Gladiator", "imgName": "角斗士Gladiator", "aka": "角斗士 Gladiator", "rating": "8.6", "year": "2000", "done": "2022-07-29"}, {"title": "活着", "imgName": "活着", "aka": "活着", "rating": "9.3", "year": "1994", "done": "2022-07-28"}, {"title": "美丽心灵 A Beautiful Mind", "imgName": "美丽心灵ABeautifulMind", "aka": "美丽心灵 A Beautiful Mind", "rating": "9.1", "year": "2001", "done": "2022-07-27"}, {"title": "宿敌 <PERSON>", "imgName": "宿敌JanaGanaMana", "aka": "宿敌 <PERSON>", "rating": "8.6", "year": "2022", "done": "2022-07-26"}, {"title": "风骚律师 第六季 Better Call Saul Season 6", "imgName": "风骚律师第六季BetterCallSaulSeason6", "aka": "风骚律师 第六季 Better Call Saul Season 6", "rating": "9.8", "year": "2022", "done": "2022-10-16"}, {"title": "真相捕捉 第二季 The Capture Season 2", "imgName": "真相捕捉第二季TheCaptureSeason2", "aka": "真相捕捉 第二季 The Capture Season 2", "rating": "9.4", "year": "2022", "done": "2022-10-15"}, {"title": "有史以来最棒的啤酒运送 The Greatest Beer Run Ever", "imgName": "有史以来最棒的啤酒运送TheGreatestBeerRunEver", "aka": "有史以来最棒的啤酒运送 The Greatest Beer Run Ever", "rating": "7.8", "year": "2022", "done": "2022-10-09"}, {"title": "子弹列车 Bullet Train", "imgName": "子弹列车BulletTrain", "aka": "子弹列车 Bullet Train", "rating": "7.9", "year": "2022", "done": "2022-10-08"}, {"title": "狩猎 헌트", "imgName": "狩猎헌트", "aka": "狩猎 헌트", "rating": "7.7", "year": "2022", "done": "2022-10-07"}, {"title": "一元换命 Dead for A Dollar", "imgName": "一元换命Dead<PERSON><PERSON><PERSON><PERSON><PERSON>", "aka": "一元换命 Dead for A Dollar", "rating": "5.7", "year": "2022", "done": "2022-10-07"}, {"title": "万里归途", "imgName": "万里归途", "aka": "万里归途", "rating": "7.4", "year": "2022", "done": "2022-10-06"}, {"title": "新神榜：杨戬", "imgName": "新神榜杨戬", "aka": "新神榜：杨戬", "rating": "7.1", "year": "2022", "done": "2022-09-12"}, {"title": "神探大战 神探大戰", "imgName": "神探大战神探大戰", "aka": "神探大战 神探大戰", "rating": "6.7", "year": "2022", "done": "2022-08-26"}, {"title": "阳光灿烂的日子", "imgName": "阳光灿烂的日子", "aka": "阳光灿烂的日子", "rating": "8.9", "year": "1994", "done": "2022-08-25"}, {"title": "隐入尘烟", "imgName": "隐入尘烟", "aka": "隐入尘烟", "rating": "8.5", "year": "2022", "done": "2022-08-24"}, {"title": "壮志凌云2：独行侠 Top Gun: Maverick", "imgName": "壮志凌云2独行侠TopGunMaverick", "aka": "壮志凌云2：独行侠 Top Gun: Maverick", "rating": "8.0", "year": "2022", "done": "2022-08-21"}, {"title": "小黄人大眼萌：神偷奶爸前传 Minions: The Rise of Gru", "imgName": "小黄人大眼萌神偷奶爸前传MinionsTheRiseofGru", "aka": "小黄人大眼萌：神偷奶爸前传 Minions: The Rise of Gru", "rating": "7.1", "year": "2022", "done": "2022-08-02"}, {"title": "分手的决心 헤어질 결심", "imgName": "分手的决心헤어질결심", "aka": "分手的决心 헤어질 결심", "rating": "7.6", "year": "2022", "done": "2022-11-14"}, {"title": "疯狂动物城+ Zootopia+", "imgName": "疯狂动物城+Zootopia+", "aka": "疯狂动物城+ Zootopia+", "rating": "8.6", "year": "2022", "done": "2022-11-13"}, {"title": "龙之家族 第一季 House of the Dragon Season 1", "imgName": "龙之家族第一季HouseoftheDragonSeason1", "aka": "龙之家族 第一季 House of the Dragon Season 1", "rating": "8.8", "year": "2022", "done": "2022-11-09"}, {"title": "共助2：国际 공조2:인터내셔날", "imgName": "共助2国际공조2인터내셔날", "aka": "共助2：国际 공조2:인터내셔날", "rating": "7.1", "year": "2022", "done": "2022-11-06"}, {"title": "黑镜 第二季 Black Mirror Season 2", "imgName": "黑镜第二季BlackMirrorSeason2", "aka": "黑镜 第二季 Black Mirror Season 2", "rating": "9.2", "year": "2013", "done": "2022-10-30"}, {"title": "西线无战事 Im Westen nichts Neues", "imgName": "西线无战事ImWestennichtsNeues", "aka": "西线无战事 Im Westen nichts Neues", "rating": "8.6", "year": "2022", "done": "2022-10-30"}, {"title": "6/45 육사오", "imgName": "645육사오", "aka": "6/45 육사오", "rating": "7.7", "year": "2022", "done": "2022-10-27"}, {"title": "万神殿 第一季 Pantheon Season 1", "imgName": "万神殿第一季PantheonSeason1", "aka": "万神殿 第一季 Pantheon Season 1", "rating": "8.9", "year": "2022", "done": "2022-10-22"}, {"title": "罗马 Roma", "imgName": "罗马Roma", "aka": "罗马 Roma", "rating": "8.2", "year": "2018", "done": "2022-10-17"}, {"title": "赛博朋克：边缘行者 Cyberpunk: Edgerunners", "imgName": "赛博朋克边缘行者CyberpunkEdgerunners", "aka": "赛博朋克：边缘行者 Cyberpunk: Edgerunners", "rating": "9.0", "year": "2022", "done": "2022-10-23"}, {"title": "琅琊榜", "imgName": "琅琊榜", "aka": "琅琊榜", "rating": "9.4", "year": "2015", "done": "2022-12-31"}, {"title": "阿凡达：水之道 Avatar: The Way of Water", "imgName": "阿凡达水之道AvatarTheWayofWater", "aka": "阿凡达：水之道 Avatar: The Way of Water", "rating": "7.9", "year": "2022", "done": "2022-12-17"}]