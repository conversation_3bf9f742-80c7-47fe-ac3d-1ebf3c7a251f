<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico" />
    <link rel="stylesheet" href="../assets/css/common.css" />
    <script src="../assets/js/<EMAIL>"></script>
    <title>电影</title>
  </head>
  <body>
    <div id="app" class="app-container">
      <template v-for="(item, index) in movieObj" :key="item.year">
        <div class="movie-year-section">
          <h1 class="title" v-if="index === 0">电影</h1>
          <h1 class="title">
            {{ item.year }}
            <span class="count" v-if="item.count">({{ item.count }})</span>
          </h1>
          <div class="movie-list-container">
            <div data-scroll="limited">
              <ul class="movie-list">
                <li
                  v-for="movie in item.list"
                  :key="movie.title + movie.year"
                  class="movie-item"
                >
                  <a
                    :href="`https://search.douban.com/movie/subject_search?search_text=${encodeURIComponent(movie.title)}`"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="movie-card"
                    @mouseover="handleHover"
                  >
                    <div class="movie-image-container">
                      <img
                        :src="`./${item.year}/images/${movie.imgName || movie.title}.jpg`"
                        @error="handleImageError"
                        class="movie-poster"
                        alt="Movie Poster"
                      />
                      <div class="glow-overlay"></div>
                    </div>
                    <div class="movie-info">
                      <div
                        :class="{ 'banned': movie.rating === '被封了' }"
                        class="movie-title"
                      >
                        {{ movie.title }} ({{ movie.year }})
                        <span class="rating rating-pop"
                          >{{ movie.rating }}</span
                        >
                      </div>
                      <div class="done done-slide">{{ movie.done }}</div>
                    </div>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </template>
    </div>

    <script>
      const YEARS = Object.freeze([
        2025, 2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015,
      ])

      new Vue({
        el: "#app",
        data: () => ({
          movieObj: [],
          total: 0,
          isLoading: false,
        }),
        async mounted() {
          await this.fetchMovieData()
        },
        methods: {
          async fetchMovieData() {
            this.isLoading = true
            try {
              const movieDataPromises = YEARS.map((year) =>
                fetch(`./${year}/data/index.json`)
                  .then((response) => {
                    if (!response.ok) throw new Error(`Failed to fetch ${year}`)
                    return response.json()
                  })
                  .then((data) => {
                    const list = data.sort((a, b) => (b.done < a.done ? -1 : 1))
                    const count = list.length
                    this.total += count
                    return {
                      list,
                      year: String(year),
                      count,
                    }
                  })
                  .catch((error) => {
                    console.warn(`Error loading data for ${year}:`, error)
                    return null
                  })
              )

              const results = await Promise.all(movieDataPromises)
              this.movieObj = results.filter(Boolean)
            } catch (error) {
              console.error("Failed to load movie data:", error)
            } finally {
              this.isLoading = false
            }
          },
          handleImageError(event) {
            event.target.src = "../assets/images/fallback.jpg"
          },
          createParticle(x, y) {
            const particle = document.createElement("div")
            particle.className = "particle"
            particle.style.left = `${x}px`
            particle.style.top = `${y}px`
            document.body.appendChild(particle)
            setTimeout(() => particle.remove(), 1000)
          },
          handleHover(event) {
            this.createParticle(event.clientX, event.clientY)
          },
        },
      })
    </script>
  </body>
</html>
