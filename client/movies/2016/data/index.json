[{"title": "驴得水", "imgName": "驴得水", "aka": "驴得水", "rating": "8.3", "year": "2016", "done": "2016-12-25"}, {"title": "血战钢锯岭 Hacksaw Ridge", "imgName": "血战钢锯岭HacksawRidge", "aka": "血战钢锯岭 Hacksaw Ridge", "rating": "8.7", "year": "2016", "done": "2016-12-13"}, {"title": "比海更深 海よりもまだ深く", "imgName": "比海更深海よりもまだ深く", "aka": "比海更深 海よりもまだ深く", "rating": "8.8", "year": "2016", "done": "2016-12-08"}, {"title": "追凶者也", "imgName": "追凶者也", "aka": "追凶者也", "rating": "7.9", "year": "2016", "done": "2016-11-20"}, {"title": "疑犯追踪 第二季 Person of Interest Season 2", "imgName": "疑犯追踪第二季PersonofInterestSeason2", "aka": "疑犯追踪 第二季 Person of Interest Season 2", "rating": "9.6", "year": "2012", "done": "2016-10-26"}, {"title": "疑犯追踪 第一季 Person of Interest Season 1", "imgName": "疑犯追踪第一季PersonofInterestSeason1", "aka": "疑犯追踪 第一季 Person of Interest Season 1", "rating": "9.3", "year": "2011", "done": "2016-10-17"}, {"title": "一个叫欧维的男人决定去死 En man som heter Ove", "imgName": "一个叫欧维的男人决定去死EnmansomheterOve", "aka": "一个叫欧维的男人决定去死 En man som heter Ove", "rating": "8.9", "year": "2015", "done": "2016-10-16"}, {"title": "黑客军团 第二季 Mr. Robot Season 2", "imgName": "黑客军团第二季MrRobotSeason2", "aka": "黑客军团 第二季 Mr. Robot Season 2", "rating": "8.2", "year": "2016", "done": "2016-10-06"}, {"title": "零日 Zero Days", "imgName": "零日ZeroDays", "aka": "零日 Zero Days", "rating": "8.2", "year": "2016", "done": "2016-10-05"}, {"title": "她 Her", "imgName": "她Her", "aka": "她 Her", "rating": "8.4", "year": "2013", "done": "2016-10-05"}, {"title": "美丽人生 La vita è bella", "imgName": "美丽人生<PERSON><PERSON><PERSON>", "aka": "美丽人生 La vita è bella", "rating": "9.5", "year": "1997", "done": "2016-10-04"}, {"title": "湄公河行动", "imgName": "湄公河行动", "aka": "湄公河行动", "rating": "8.0", "year": "2016", "done": "2016-10-04"}, {"title": "火海凌云 Экипаж", "imgName": "火海凌云Экипаж", "aka": "火海凌云 Экипаж", "rating": "7.7", "year": "2016", "done": "2016-10-04"}, {"title": "美丽心灵 A Beautiful Mind", "imgName": "美丽心灵ABeautifulMind", "aka": "美丽心灵 A Beautiful Mind", "rating": "9.1", "year": "2001", "done": "2016-10-03"}, {"title": "超新约全书 Le tout nouveau testament", "imgName": "超新约全书Letoutnouveautestament", "aka": "超新约全书 Le tout nouveau testament", "rating": "7.8", "year": "2015", "done": "2016-10-03"}, {"title": "世界小姐2016 Miss World 2016", "imgName": "世界小姐2016MissWorld2016", "aka": "世界小姐2016 Miss World 2016", "rating": "", "year": "2016", "done": "2016-10-02"}, {"title": "瑞士军刀男 Swiss Army Man", "imgName": "瑞士军刀男SwissArmyMan", "aka": "瑞士军刀男 Swiss Army Man", "rating": "7.8", "year": "2016", "done": "2016-10-01"}, {"title": "星际迷航3：超越星辰 Star Trek Beyond", "imgName": "星际迷航3超越星辰StarTrekBeyond", "aka": "星际迷航3：超越星辰 Star Trek Beyond", "rating": "7.5", "year": "2016", "done": "2016-10-01"}, {"title": "追捕野蛮人 Hunt for the Wilderpeople", "imgName": "追捕野蛮人HuntfortheWilderpeople", "aka": "追捕野蛮人 Hunt for the Wilderpeople", "rating": "8.1", "year": "2016", "done": "2016-09-25"}, {"title": "隧道 터널", "imgName": "隧道터널", "aka": "隧道 터널", "rating": "7.9", "year": "2016", "done": "2016-09-23"}, {"title": "你的名字。 君の名は。", "imgName": "你的名字君の名は", "aka": "你的名字。 君の名は。", "rating": "8.5", "year": "2016", "done": "2016-09-16"}, {"title": "釜山行 부산행", "imgName": "釜山行부산행", "aka": "釜山行 부산행", "rating": "8.6", "year": "2016", "done": "2016-09-16"}, {"title": "危城", "imgName": "危城", "aka": "危城", "rating": "6.0", "year": "2016", "done": "2016-01-01"}, {"title": "惊天大逆转", "imgName": "惊天大逆转", "aka": "惊天大逆转", "rating": "6.9", "year": "2016", "done": "2016-01-01"}, {"title": "寒战2 寒戰II", "imgName": "寒战2寒戰II", "aka": "寒战2 寒戰II", "rating": "7.2", "year": "2016", "done": "2016-01-01"}, {"title": "海蒂和爷爷 Heidi", "imgName": "海蒂和爷爷Heidi", "aka": "海蒂和爷爷 Heidi", "rating": "9.3", "year": "2015", "done": "2016-01-01"}, {"title": "谍影重重5 <PERSON>", "imgName": "谍影重重5JasonBourne", "aka": "谍影重重5 <PERSON>", "rating": "7.5", "year": "2016", "done": "2016-01-01"}, {"title": "完美陌生人 <PERSON>tti sconosciuti", "imgName": "完美陌生人Per<PERSON>ttisconosciuti", "aka": "完美陌生人 <PERSON>tti sconosciuti", "rating": "8.5", "year": "2016", "done": "2016-01-01"}, {"title": "X战警：天启 X-Men: Apocalypse", "imgName": "X战警天启X-MenApocalypse", "aka": "X战警：天启 X-Men: Apocalypse", "rating": "7.7", "year": "2016", "done": "2016-01-01"}, {"title": "超能人鱼", "imgName": "超能人鱼", "aka": "超能人鱼", "rating": "", "year": "2016", "done": "2016-01-01"}, {"title": "魔兽 Warcraft", "imgName": "魔兽Warcraft", "aka": "魔兽 Warcraft", "rating": "7.7", "year": "2016", "done": "2016-01-01"}, {"title": "奇幻森林 The Jungle Book", "imgName": "奇幻森林TheJungleBook", "aka": "奇幻森林 The Jungle Book", "rating": "7.8", "year": "2016", "done": "2016-01-01"}, {"title": "地雷区 Under sandet", "imgName": "地雷区Undersandet", "aka": "地雷区 Under sandet", "rating": "8.7", "year": "2015", "done": "2016-01-01"}, {"title": "疯狂动物城 Zootopia", "imgName": "疯狂动物城Zootopia", "aka": "疯狂动物城 Zootopia", "rating": "9.2", "year": "2016", "done": "2016-01-01"}, {"title": "大空头 The Big Short", "imgName": "大空头TheBigShort", "aka": "大空头 The Big Short", "rating": "8.5", "year": "2015", "done": "2016-01-01"}, {"title": "荒野猎人 The Revenant", "imgName": "荒野猎人TheRevenant", "aka": "荒野猎人 The Revenant", "rating": "8.0", "year": "2015", "done": "2016-01-01"}, {"title": "师父", "imgName": "师父", "aka": "师父", "rating": "8.2", "year": "2015", "done": "2016-01-01"}, {"title": "寻龙诀", "imgName": "寻龙诀", "aka": "寻龙诀", "rating": "7.4", "year": "2015", "done": "2016-01-01"}, {"title": "火星救援 The Martian", "imgName": "火星救援TheMartian", "aka": "火星救援 The Martian", "rating": "8.5", "year": "2015", "done": "2016-01-01"}, {"title": "北京遇上西雅图之不二情书", "imgName": "北京遇上西雅图之不二情书", "aka": "北京遇上西雅图之不二情书", "rating": "6.4", "year": "2016", "done": "2016-01-01"}, {"title": "唐人街探案", "imgName": "唐人街探案", "aka": "唐人街探案", "rating": "7.7", "year": "2015", "done": "2016-01-01"}, {"title": "泰山归来：险战丛林 The Legend of Tarzan", "imgName": "泰山归来险战丛林TheLegendofTarzan", "aka": "泰山归来：险战丛林 The Legend of Tarzan", "rating": "6.1", "year": "2016", "done": "2016-01-01"}, {"title": "八恶人 The Hateful Eight", "imgName": "八恶人TheHatefulEight", "aka": "八恶人 The Hateful Eight", "rating": "8.6", "year": "2015", "done": "2016-01-01"}, {"title": "西游记之孙悟空三打白骨精", "imgName": "西游记之孙悟空三打白骨精", "aka": "西游记之孙悟空三打白骨精", "rating": "5.6", "year": "2016", "done": "2016-01-01"}, {"title": "踏血寻梅 踏血尋梅", "imgName": "踏血寻梅踏血尋梅", "aka": "踏血寻梅 踏血尋梅", "rating": "7.5", "year": "2015", "done": "2016-01-01"}, {"title": "恶棍天使", "imgName": "恶棍天使", "aka": "恶棍天使", "rating": "4.4", "year": "2015", "done": "2016-01-01"}, {"title": "间谍之桥 Bridge of Spies", "imgName": "间谍之桥BridgeofSpies", "aka": "间谍之桥 Bridge of Spies", "rating": "8.2", "year": "2015", "done": "2016-01-01"}, {"title": "飞鹰艾迪 Eddie the Eagle", "imgName": "飞鹰艾迪<PERSON>the<PERSON><PERSON><PERSON>", "aka": "飞鹰艾迪 Eddie the Eagle", "rating": "8.1", "year": "2016", "done": "2016-01-01"}, {"title": "火锅英雄", "imgName": "火锅英雄", "aka": "火锅英雄", "rating": "7.2", "year": "2016", "done": "2016-01-01"}, {"title": "叶问3", "imgName": "叶问3", "aka": "叶问3", "rating": "6.7", "year": "2015", "done": "2016-01-01"}, {"title": "小门神", "imgName": "小门神", "aka": "小门神", "rating": "6.9", "year": "2016", "done": "2016-01-01"}, {"title": "死侍 Deadpool", "imgName": "死侍Deadpool", "aka": "死侍 Deadpool", "rating": "7.9", "year": "2016", "done": "2016-01-01"}]