[{"title": "海上钢琴师 La leggenda del pianista sull'oceano", "imgName": "海上钢琴师Laleggendadelpianistasulloceano", "aka": "海上钢琴师 La leggenda del pianista sull'oceano", "rating": "9.3", "year": "1998", "done": "2015-01-01"}, {"title": "千与千寻 千と千尋の神隠し", "imgName": "千与千寻千と千尋の神隠し", "aka": "千与千寻 千と千尋の神隠し", "rating": "9.4", "year": "2001", "done": "2015-01-01"}, {"title": "阿甘正传 <PERSON>", "imgName": "阿甘正传ForrestGump", "aka": "阿甘正传 <PERSON>", "rating": "9.5", "year": "1994", "done": "2015-01-01"}, {"title": "这个杀手不太冷 Léon", "imgName": "这个杀手不太冷Léon", "aka": "这个杀手不太冷 Léon", "rating": "9.4", "year": "1994", "done": "2015-01-01"}, {"title": "肖申克的救赎 The Shawshank Redemption", "imgName": "肖申克的救赎TheShawshankRedemption", "aka": "肖申克的救赎 The Shawshank Redemption", "rating": "9.7", "year": "1994", "done": "2015-01-01"}, {"title": "冰川时代 Ice Age", "imgName": "冰川时代IceAge", "aka": "冰川时代 Ice Age", "rating": "8.6", "year": "2002", "done": "2015-01-01"}, {"title": "杀人回忆 살인의 추억", "imgName": "杀人回忆살인의추억", "aka": "杀人回忆 살인의 추억", "rating": "8.9", "year": "2003", "done": "2015-01-01"}, {"title": "完美的世界 A Perfect World", "imgName": "完美的世界APerfectWorld", "aka": "完美的世界 A Perfect World", "rating": "9.1", "year": "1993", "done": "2015-01-01"}, {"title": "疯狂原始人 The Croods", "imgName": "疯狂原始人TheCroods", "aka": "疯狂原始人 The Croods", "rating": "8.7", "year": "2013", "done": "2015-01-01"}, {"title": "贫民窟的百万富翁 Slumdog Millionaire", "imgName": "贫民窟的百万富翁SlumdogMillionaire", "aka": "贫民窟的百万富翁 Slumdog Millionaire", "rating": "8.6", "year": "2008", "done": "2015-01-01"}, {"title": "神偷奶爸 Despicable Me", "imgName": "神偷奶爸DespicableMe", "aka": "神偷奶爸 Despicable Me", "rating": "8.7", "year": "2010", "done": "2015-01-01"}, {"title": "猫鼠游戏 Catch Me If You Can", "imgName": "猫鼠游戏CatchMeIfYouCan", "aka": "猫鼠游戏 Catch Me If You Can", "rating": "9.1", "year": "2002", "done": "2015-01-01"}, {"title": "超脱 Detachment", "imgName": "超脱Detachment", "aka": "超脱 Detachment", "rating": "9.0", "year": "2011", "done": "2015-01-01"}, {"title": "阿凡达 Avatar", "imgName": "阿凡达Avatar", "aka": "阿凡达 Avatar", "rating": "8.8", "year": "2009", "done": "2015-01-01"}, {"title": "加勒比海盗 Pirates of the Caribbean: The Curse of the Black Pearl", "imgName": "加勒比海盗PiratesoftheCaribbeanTheCurseoftheBlackPearl", "aka": "加勒比海盗 Pirates of the Caribbean: The Curse of the Black Pearl", "rating": "8.8", "year": "2003", "done": "2015-01-01"}, {"title": "禁闭岛 Shutter Island", "imgName": "禁闭岛ShutterIsland", "aka": "禁闭岛 Shutter Island", "rating": "8.9", "year": "2010", "done": "2015-01-01"}, {"title": "上帝之城 Cidade de Deus", "imgName": "上帝之城CidadedeDeus", "aka": "上帝之城 Cidade de Deus", "rating": "9.0", "year": "2002", "done": "2015-01-01"}, {"title": "阳光姐妹淘 써니", "imgName": "阳光姐妹淘써니", "aka": "阳光姐妹淘 써니", "rating": "8.8", "year": "2011", "done": "2015-01-01"}, {"title": "辩护人 변호인", "imgName": "辩护人변호인", "aka": "辩护人 변호인", "rating": "9.2", "year": "2013", "done": "2015-01-01"}, {"title": "让子弹飞", "imgName": "让子弹飞", "aka": "让子弹飞", "rating": "9.0", "year": "2010", "done": "2015-01-01"}, {"title": "射雕英雄传之东成西就 射鵰英雄傳之東成西就", "imgName": "射雕英雄传之东成西就射鵰英雄傳之東成西就", "aka": "射雕英雄传之东成西就 射鵰英雄傳之東成西就", "rating": "8.7", "year": "1993", "done": "2015-01-01"}, {"title": "致命魔术 The Prestige", "imgName": "致命魔术ThePrestige", "aka": "致命魔术 The Prestige", "rating": "8.9", "year": "2006", "done": "2015-01-01"}, {"title": "钢琴家 The Pianist", "imgName": "钢琴家ThePianist", "aka": "钢琴家 The Pianist", "rating": "9.3", "year": "2002", "done": "2015-01-01"}, {"title": "勇敢的心 Braveheart", "imgName": "勇敢的心Braveheart", "aka": "勇敢的心 Braveheart", "rating": "8.9", "year": "1995", "done": "2015-01-01"}, {"title": "情书 Love Letter", "imgName": "情书LoveLetter", "aka": "情书 Love Letter", "rating": "8.9", "year": "1995", "done": "2015-01-01"}, {"title": "七宗罪 Se7en", "imgName": "七宗罪Se7en", "aka": "七宗罪 Se7en", "rating": "8.8", "year": "1995", "done": "2015-01-01"}, {"title": "V字仇杀队 V for Vendetta", "imgName": "V字仇杀队", "aka": "V字仇杀队", "rating": "封杀了", "year": "2005", "done": "2015-01-01"}, {"title": "熔炉 도가니", "imgName": "熔炉도가니", "aka": "熔炉 도가니", "rating": "9.4", "year": "2011", "done": "2015-01-01"}, {"title": "搏击俱乐部 Fight Club", "imgName": "搏击俱乐部FightClub", "aka": "搏击俱乐部 Fight Club", "rating": "9.0", "year": "1999", "done": "2015-01-01"}, {"title": "大话西游之大圣娶亲 西遊記大結局之仙履奇緣", "imgName": "大话西游之大圣娶亲西遊記大結局之仙履奇緣", "aka": "大话西游之大圣娶亲 西遊記大結局之仙履奇緣", "rating": "9.2", "year": "1995", "done": "2015-01-01"}, {"title": "龙猫 となりのトトロ", "imgName": "龙猫となりのトトロ", "aka": "龙猫 となりのトトロ", "rating": "9.2", "year": "1988", "done": "2015-01-01"}, {"title": "放牛班的春天 Les choristes", "imgName": "放牛班的春天Leschoristes", "aka": "放牛班的春天 Les choristes", "rating": "9.3", "year": "2004", "done": "2015-01-01"}, {"title": "三傻大闹宝莱坞 3 Idiots", "imgName": "三傻大闹宝莱坞3Idiots", "aka": "三傻大闹宝莱坞 3 Idiots", "rating": "9.2", "year": "2009", "done": "2015-01-01"}, {"title": "盗梦空间 Inception", "imgName": "盗梦空间Inception", "aka": "盗梦空间 Inception", "rating": "9.4", "year": "2010", "done": "2015-01-01"}, {"title": "机器人总动员 WALL·E", "imgName": "机器人总动员WALL·E", "aka": "机器人总动员 WALL·E", "rating": "9.3", "year": "2008", "done": "2015-01-01"}, {"title": "恐怖直播 더 테러 라이브", "imgName": "恐怖直播더테러라이브", "aka": "恐怖直播 더 테러 라이브", "rating": "8.7", "year": "2013", "done": "2015-01-01"}, {"title": "人工智能 A.I. Artificial Intelligence", "imgName": "人工智能AIArtificialIntelligence", "aka": "人工智能 A.I. Artificial Intelligence", "rating": "8.7", "year": "2001", "done": "2015-01-01"}, {"title": "玩具总动员3 Toy Story 3", "imgName": "玩具总动员3ToyStory3", "aka": "玩具总动员3 Toy Story 3", "rating": "8.9", "year": "2010", "done": "2015-01-01"}, {"title": "英雄本色", "imgName": "英雄本色", "aka": "英雄本色", "rating": "8.6", "year": "1986", "done": "2015-01-01"}, {"title": "怪兽电力公司 Monsters, Inc.", "imgName": "怪兽电力公司MonstersInc", "aka": "怪兽电力公司 Monsters, Inc.", "rating": "8.8", "year": "2001", "done": "2015-01-01"}, {"title": "素媛 소원", "imgName": "素媛소원", "aka": "素媛 소원", "rating": "9.3", "year": "2013", "done": "2015-01-01"}, {"title": "拯救大兵瑞恩 Saving Private Ryan", "imgName": "拯救大兵瑞恩SavingPrivateRyan", "aka": "拯救大兵瑞恩 Saving Private Ryan", "rating": "9.1", "year": "1998", "done": "2015-01-01"}, {"title": "黑客帝国 The Matrix", "imgName": "黑客帝国TheMatrix", "aka": "黑客帝国 The Matrix", "rating": "9.1", "year": "1999", "done": "2015-01-01"}, {"title": "星际穿越 Interstellar", "imgName": "星际穿越Interstellar", "aka": "星际穿越 Interstellar", "rating": "9.4", "year": "2014", "done": "2015-01-01"}, {"title": "天空之城 天空の城ラピュタ", "imgName": "天空之城天空の城ラピュタ", "aka": "天空之城 天空の城ラピュタ", "rating": "9.2", "year": "1986", "done": "2015-01-01"}, {"title": "两杆大烟枪 Lock, Stock and Two Smoking Barrels", "imgName": "两杆大烟枪LockStockandTwoSmokingBarrels", "aka": "两杆大烟枪 Lock, Stock and Two Smoking Barrels", "rating": "9.1", "year": "1998", "done": "2015-01-01"}, {"title": "飞屋环游记 Up", "imgName": "飞屋环游记Up", "aka": "飞屋环游记 Up", "rating": "9.1", "year": "2009", "done": "2015-01-01"}, {"title": "大话西游之月光宝盒 西遊記第壹佰零壹回之月光寶盒", "imgName": "大话西游之月光宝盒西遊記第壹佰零壹回之月光寶盒", "aka": "大话西游之月光宝盒 西遊記第壹佰零壹回之月光寶盒", "rating": "9.0", "year": "1995", "done": "2015-01-01"}, {"title": "无间道 無間道", "imgName": "无间道無間道", "aka": "无间道 無間道", "rating": "9.3", "year": "2002", "done": "2015-01-01"}, {"title": "鬼子来了", "imgName": "鬼子来了", "aka": "鬼子来了", "rating": "9.3", "year": "2000", "done": "2015-01-01"}, {"title": "窃听风暴 Das Leben der Anderen", "imgName": "窃听风暴DasLebenderAnderen", "aka": "窃听风暴 Das Leben der Anderen", "rating": "9.2", "year": "2006", "done": "2015-01-01"}, {"title": "少年派的奇幻漂流 Life of Pi", "imgName": "少年派的奇幻漂流LifeofPi", "aka": "少年派的奇幻漂流 Life of Pi", "rating": "9.1", "year": "2012", "done": "2015-01-01"}, {"title": "假如爱有天意 클래식", "imgName": "假如爱有天意클래식", "aka": "假如爱有天意 클래식", "rating": "8.4", "year": "2003", "done": "2015-01-01"}, {"title": "超能陆战队 Big Hero 6", "imgName": "超能陆战队BigHero6", "aka": "超能陆战队 Big Hero 6", "rating": "8.8", "year": "2014", "done": "2015-01-01"}, {"title": "血钻 Blood Diamond", "imgName": "血钻Blood<PERSON>iamond", "aka": "血钻 Blood Diamond", "rating": "8.7", "year": "2006", "done": "2015-01-01"}, {"title": "无敌破坏王 Wreck-It Ralph", "imgName": "无敌破坏王Wreck-ItRalph", "aka": "无敌破坏王 Wreck-It Ralph", "rating": "8.7", "year": "2012", "done": "2015-01-01"}, {"title": "被解救的姜戈 Django Unchained", "imgName": "被解救的姜戈DjangoUnchained", "aka": "被解救的姜戈 Django Unchained", "rating": "8.8", "year": "2012", "done": "2015-01-01"}, {"title": "心迷宫", "imgName": "心迷宫", "aka": "心迷宫", "rating": "8.8", "year": "2014", "done": "2015-01-01"}, {"title": "黑客帝国3：矩阵革命 The Matrix Revolutions", "imgName": "黑客帝国3矩阵革命TheMatrixRevolutions", "aka": "黑客帝国3：矩阵革命 The Matrix Revolutions", "rating": "8.8", "year": "2003", "done": "2015-01-01"}, {"title": "源代码 Source Code", "imgName": "源代码SourceCode", "aka": "源代码 Source Code", "rating": "8.5", "year": "2011", "done": "2015-01-01"}, {"title": "初恋这件小事 สิ่งเล็กเล็กที่เรียกว่า...รัก", "imgName": "初恋这件小事สิ่งเล็กเล็กที่เรียกว่ารัก", "aka": "初恋这件小事 สิ่งเล็กเล็กที่เรียกว่า...รัก", "rating": "8.5", "year": "2010", "done": "2015-01-01"}, {"title": "唐伯虎点秋香 唐伯虎點秋香", "imgName": "唐伯虎点秋香唐伯虎點秋香", "aka": "唐伯虎点秋香 唐伯虎點秋香", "rating": "8.7", "year": "1993", "done": "2015-01-01"}, {"title": "喜剧之王 喜劇之王", "imgName": "喜剧之王喜劇之王", "aka": "喜剧之王 喜劇之王", "rating": "8.8", "year": "1999", "done": "2015-01-01"}, {"title": "7号房的礼物 7번방의 선물", "imgName": "7号房的礼物7번방의선물", "aka": "7号房的礼物 7번방의 선물", "rating": "8.9", "year": "2013", "done": "2015-01-01"}, {"title": "浪潮 Die Welle", "imgName": "浪潮DieWelle", "aka": "浪潮 Die Welle", "rating": "8.7", "year": "2008", "done": "2015-01-01"}]