const fs = require('fs')
const path = require('path')
const MarkdownSyncer = require('./syncToMarkdown')

class BidirectionalSyncer extends MarkdownSyncer {
  constructor() {
    super()
  }

  // 解析 Markdown 文件中的电影数据
  parseMarkdownMovies() {
    try {
      if (!fs.existsSync(this.markdownPath)) {
        console.log('❌ Markdown 文件不存在')
        return {}
      }

      const content = fs.readFileSync(this.markdownPath, 'utf8')
      const lines = content.split('\n')
      const moviesByYear = {}

      for (const line of lines) {
        const trimmedLine = line.trim()
        
        // 匹配电影条目格式: YYYY-MM-DD 电影名
        const match = trimmedLine.match(/^(\d{4})-(\d{2})-(\d{2})\s+(.+)$/)
        
        if (match) {
          const [, year, month, day, titlePart] = match
          const done = `${year}-${month}-${day}`
          
          // 处理被封的电影
          let title = titlePart
          let rating = '0.0'
          
          if (titlePart.includes('[被封]')) {
            title = titlePart.replace(/\s*\[被封\]\s*$/, '')
            rating = '被封了'
          }
          
          // 提取英文标题（如果有）
          const titleMatch = title.match(/^(.+?)\s+([A-Za-z].*)$/)
          let chineseTitle = title
          let englishTitle = ''
          
          if (titleMatch) {
            chineseTitle = titleMatch[1].trim()
            englishTitle = titleMatch[2].trim()
          }

          const movieData = {
            title: chineseTitle,
            imgName: chineseTitle.replace(/[`~!@#$^&*()=|{}':;',\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g, ''),
            aka: title,
            rating: rating,
            year: year,
            done: done
          }

          if (!moviesByYear[year]) {
            moviesByYear[year] = []
          }
          
          moviesByYear[year].push(movieData)
        }
      }

      // 按日期排序每年的电影
      Object.keys(moviesByYear).forEach(year => {
        moviesByYear[year].sort((a, b) => new Date(b.done) - new Date(a.done))
      })

      return moviesByYear
      
    } catch (error) {
      console.error('❌ 解析 Markdown 失败:', error.message)
      return {}
    }
  }

  // 从 Markdown 同步到 JSON
  async syncFromMarkdown() {
    try {
      console.log('🔄 开始从 Markdown 同步到 JSON...')
      
      const moviesByYear = this.parseMarkdownMovies()
      
      if (Object.keys(moviesByYear).length === 0) {
        console.log('⚠️ Markdown 中没有找到电影数据')
        return false
      }

      let updatedYears = 0
      let totalMovies = 0

      for (const [year, movies] of Object.entries(moviesByYear)) {
        const yearDir = path.join(this.moviesDir, year)
        const dataDir = path.join(yearDir, 'data')
        const jsonPath = path.join(dataDir, 'index.json')

        // 创建目录
        if (!fs.existsSync(yearDir)) {
          fs.mkdirSync(yearDir, { recursive: true })
        }
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true })
        }

        // 备份现有 JSON 文件
        if (fs.existsSync(jsonPath)) {
          const backupPath = jsonPath + '.backup.' + Date.now()
          fs.copyFileSync(jsonPath, backupPath)
        }

        // 写入新的 JSON 数据
        fs.writeFileSync(jsonPath, JSON.stringify(movies, null, '\t'), 'utf8')
        
        updatedYears++
        totalMovies += movies.length
        
        console.log(`✅ 已更新 ${year} 年数据: ${movies.length} 部电影`)
      }

      console.log('🎉 从 Markdown 同步到 JSON 完成!')
      console.log(`📊 统计信息:`)
      console.log(`   - 更新年份: ${updatedYears} 年`)
      console.log(`   - 总电影数: ${totalMovies} 部`)

      return true
      
    } catch (error) {
      console.error('❌ 从 Markdown 同步失败:', error.message)
      return false
    }
  }

  // 智能同步：检测哪个文件更新，然后同步
  async smartSync() {
    try {
      console.log('🧠 开始智能同步...')
      
      // 检查文件修改时间
      const markdownStat = fs.existsSync(this.markdownPath) ? fs.statSync(this.markdownPath) : null
      
      // 检查最新的 JSON 文件修改时间
      let latestJsonTime = 0
      const years = fs.readdirSync(this.moviesDir)
        .filter(item => {
          const fullPath = path.join(this.moviesDir, item)
          return fs.statSync(fullPath).isDirectory() && /^\d{4}$/.test(item)
        })

      for (const year of years) {
        const jsonPath = path.join(this.moviesDir, year, 'data', 'index.json')
        if (fs.existsSync(jsonPath)) {
          const stat = fs.statSync(jsonPath)
          latestJsonTime = Math.max(latestJsonTime, stat.mtime.getTime())
        }
      }

      if (!markdownStat) {
        console.log('📝 Markdown 文件不存在，从 JSON 创建')
        return await this.syncToMarkdown()
      }

      const markdownTime = markdownStat.mtime.getTime()

      if (markdownTime > latestJsonTime) {
        console.log('📝 Markdown 文件更新，同步到 JSON')
        return await this.syncFromMarkdown()
      } else if (latestJsonTime > markdownTime) {
        console.log('📊 JSON 文件更新，同步到 Markdown')
        return await this.syncToMarkdown()
      } else {
        console.log('✅ 文件已同步，无需更新')
        return true
      }
      
    } catch (error) {
      console.error('❌ 智能同步失败:', error.message)
      return false
    }
  }

  // 合并冲突：当两边都有更新时
  async mergeConflicts() {
    try {
      console.log('🔀 开始合并冲突...')
      
      const markdownMovies = this.parseMarkdownMovies()
      const jsonMovies = await this.getAllMovieData()
      
      const mergedMovies = {}
      
      // 合并所有年份
      const allYears = new Set([
        ...Object.keys(markdownMovies),
        ...Object.keys(jsonMovies)
      ])
      
      for (const year of allYears) {
        const mdMovies = markdownMovies[year] || []
        const jsMovies = jsonMovies[year] || []
        
        // 使用 Map 来去重，以 title + done 为键
        const movieMap = new Map()
        
        // 先添加 JSON 中的电影（优先级高，因为有更多信息）
        jsMovies.forEach(movie => {
          const key = `${movie.title}_${movie.done}`
          movieMap.set(key, movie)
        })
        
        // 再添加 Markdown 中的电影（如果不存在的话）
        mdMovies.forEach(movie => {
          const key = `${movie.title}_${movie.done}`
          if (!movieMap.has(key)) {
            movieMap.set(key, movie)
          }
        })
        
        mergedMovies[year] = Array.from(movieMap.values())
          .sort((a, b) => new Date(b.done) - new Date(a.done))
      }
      
      // 保存合并结果
      await this.saveMovieData(mergedMovies)
      
      console.log('✅ 冲突合并完成!')
      return true
      
    } catch (error) {
      console.error('❌ 合并冲突失败:', error.message)
      return false
    }
  }

  // 保存电影数据到 JSON 和 Markdown
  async saveMovieData(moviesByYear) {
    // 保存到 JSON
    for (const [year, movies] of Object.entries(moviesByYear)) {
      const yearDir = path.join(this.moviesDir, year)
      const dataDir = path.join(yearDir, 'data')
      const jsonPath = path.join(dataDir, 'index.json')

      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true })
      }

      fs.writeFileSync(jsonPath, JSON.stringify(movies, null, '\t'), 'utf8')
    }

    // 保存到 Markdown
    const markdownContent = this.generateMarkdownContent(moviesByYear)
    fs.writeFileSync(this.markdownPath, markdownContent, 'utf8')
  }
}

module.exports = BidirectionalSyncer
