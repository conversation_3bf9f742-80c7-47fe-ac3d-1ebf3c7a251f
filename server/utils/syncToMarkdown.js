const fs = require('fs')
const path = require('path')

class MarkdownSyncer {
  constructor() {
    this.markdownPath = path.join(__dirname, '../../看过的电影.md')
    this.moviesDir = path.join(__dirname, '../../client/movies')
  }

  // 读取所有年份的 JSON 数据
  async getAllMovieData() {
    const moviesByYear = {}
    
    try {
      const years = fs.readdirSync(this.moviesDir)
        .filter(item => {
          const fullPath = path.join(this.moviesDir, item)
          return fs.statSync(fullPath).isDirectory() && /^\d{4}$/.test(item)
        })
        .sort((a, b) => parseInt(b) - parseInt(a)) // 降序排列

      for (const year of years) {
        const jsonPath = path.join(this.moviesDir, year, 'data', 'index.json')
        
        if (fs.existsSync(jsonPath)) {
          const jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'))
          moviesByYear[year] = jsonData.sort((a, b) => {
            // 按观看日期降序排列
            return new Date(b.done) - new Date(a.done)
          })
        }
      }

      return moviesByYear
    } catch (error) {
      console.error('读取电影数据失败:', error.message)
      return {}
    }
  }

  // 格式化电影条目为 Markdown 格式
  formatMovieEntry(movie) {
    const { title, done, rating } = movie
    
    // 处理被封的电影
    if (rating === '被封了') {
      return `${done} ${title} [被封]`
    }
    
    // 正常电影格式
    return `${done} ${title}`
  }

  // 生成 Markdown 内容
  generateMarkdownContent(moviesByYear) {
    let content = '# Movies\n\n'
    
    const years = Object.keys(moviesByYear).sort((a, b) => parseInt(b) - parseInt(a))
    
    for (let i = 0; i < years.length; i++) {
      const year = years[i]
      const movies = moviesByYear[year]
      
      if (movies.length === 0) continue
      
      // 添加年份分隔线
      content += '=====================================================================================================================\n\n'
      
      // 添加电影条目
      movies.forEach(movie => {
        content += this.formatMovieEntry(movie) + '\n\n'
      })
    }
    
    return content
  }

  // 同步到 Markdown 文件
  async syncToMarkdown() {
    try {
      console.log('🔄 开始同步 JSON 数据到 Markdown...')
      
      // 读取所有电影数据
      const moviesByYear = await this.getAllMovieData()
      
      if (Object.keys(moviesByYear).length === 0) {
        console.log('⚠️ 没有找到电影数据')
        return false
      }
      
      // 生成 Markdown 内容
      const markdownContent = this.generateMarkdownContent(moviesByYear)
      
      // 备份原文件
      if (fs.existsSync(this.markdownPath)) {
        const backupPath = this.markdownPath + '.backup.' + Date.now()
        fs.copyFileSync(this.markdownPath, backupPath)
        console.log(`📋 已备份原文件到: ${path.basename(backupPath)}`)
      }
      
      // 写入新内容
      fs.writeFileSync(this.markdownPath, markdownContent, 'utf8')
      
      // 统计信息
      const totalMovies = Object.values(moviesByYear).reduce((sum, movies) => sum + movies.length, 0)
      const yearCount = Object.keys(moviesByYear).length
      
      console.log('✅ 同步完成!')
      console.log(`📊 统计信息:`)
      console.log(`   - 总电影数: ${totalMovies} 部`)
      console.log(`   - 年份数: ${yearCount} 年`)
      console.log(`   - 文件路径: ${this.markdownPath}`)
      
      return true
      
    } catch (error) {
      console.error('❌ 同步失败:', error.message)
      return false
    }
  }

  // 验证同步结果
  async validateSync() {
    try {
      if (!fs.existsSync(this.markdownPath)) {
        console.log('❌ Markdown 文件不存在')
        return false
      }
      
      const content = fs.readFileSync(this.markdownPath, 'utf8')
      const lines = content.split('\n').filter(line => line.trim())
      
      // 统计电影条目（排除标题和分隔线）
      const movieLines = lines.filter(line => 
        line.match(/^\d{4}-\d{2}-\d{2}/) && 
        !line.includes('=====')
      )
      
      console.log('🔍 验证结果:')
      console.log(`   - Markdown 总行数: ${lines.length}`)
      console.log(`   - 电影条目数: ${movieLines.length}`)
      
      return true
      
    } catch (error) {
      console.error('❌ 验证失败:', error.message)
      return false
    }
  }

  // 比较 JSON 和 Markdown 的差异
  async compareData() {
    try {
      const moviesByYear = await this.getAllMovieData()
      const jsonMovieCount = Object.values(moviesByYear).reduce((sum, movies) => sum + movies.length, 0)
      
      if (fs.existsSync(this.markdownPath)) {
        const content = fs.readFileSync(this.markdownPath, 'utf8')
        const movieLines = content.split('\n').filter(line => 
          line.match(/^\d{4}-\d{2}-\d{2}/)
        )
        
        console.log('📊 数据对比:')
        console.log(`   - JSON 电影数: ${jsonMovieCount}`)
        console.log(`   - Markdown 电影数: ${movieLines.length}`)
        console.log(`   - 差异: ${Math.abs(jsonMovieCount - movieLines.length)}`)
        
        return {
          jsonCount: jsonMovieCount,
          markdownCount: movieLines.length,
          difference: Math.abs(jsonMovieCount - movieLines.length)
        }
      }
      
      return null
      
    } catch (error) {
      console.error('❌ 比较失败:', error.message)
      return null
    }
  }
}

module.exports = MarkdownSyncer
