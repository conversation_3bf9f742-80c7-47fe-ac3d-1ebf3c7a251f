// utils/downloadImg.js
const https = require("https")
const fs = require("fs")
const path = require("path")

async function downloadImg(url, savePath, referer) {
  const options = {
    headers: {
      Referer: referer,
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    },
  }

  return new Promise((resolve, reject) => {
    https
      .get(url, options, (res) => {
        if (res.statusCode === 403) {
          console.error("图片下载失败，可能由于防盗链策略。")
          console.log("Request headers:", res.req._headers)
          reject(new Error("403 Forbidden"))
          return
        }

        let imgData = ""
        res.setEncoding("binary")
        res.on("data", (chunk) => {
          imgData += chunk
        })
        res.on("end", async () => {
          try {
            await fs.promises.mkdir(path.dirname(savePath), { recursive: true })
            await fs.promises.writeFile(savePath, imgData, "binary")
            console.log(`${referer} 图片下载成功`)
            resolve()
          } catch (err) {
            reject(err)
          }
        })
      })
      .on("error", (err) => {
        console.error("图片下载出错:", err)
        reject(err)
      })
  })
}

module.exports = downloadImg
