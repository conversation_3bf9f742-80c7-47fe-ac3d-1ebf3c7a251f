const fs = require('fs')
const path = require('path')

class CookieManager {
  constructor(cookieFilePath = path.join(__dirname, '../cookies.json')) {
    this.cookieFilePath = cookieFilePath
  }

  // 保存 cookies
  async saveCookies(page) {
    try {
      const cookies = await page.cookies()
      const cookieData = {
        cookies,
        timestamp: Date.now(),
        userAgent: await page.evaluate(() => navigator.userAgent)
      }
      
      fs.writeFileSync(this.cookieFilePath, JSON.stringify(cookieData, null, 2))
      console.log('✅ Cookies 已保存')
      return true
    } catch (error) {
      console.error('❌ 保存 Cookies 失败:', error.message)
      return false
    }
  }

  // 加载 cookies
  async loadCookies(page) {
    try {
      if (!fs.existsSync(this.cookieFilePath)) {
        console.log('📝 未找到已保存的 Cookies')
        return false
      }

      const cookieData = JSON.parse(fs.readFileSync(this.cookieFilePath))
      
      // 检查 cookies 是否过期（7天）
      const sevenDays = 7 * 24 * 60 * 60 * 1000
      if (Date.now() - cookieData.timestamp > sevenDays) {
        console.log('⏰ Cookies 已过期，需要重新登录')
        this.clearCookies()
        return false
      }

      // 设置 User-Agent
      if (cookieData.userAgent) {
        await page.setUserAgent(cookieData.userAgent)
      }

      // 加载 cookies
      await page.setCookie(...cookieData.cookies)
      console.log('✅ Cookies 已加载')
      return true
    } catch (error) {
      console.error('❌ 加载 Cookies 失败:', error.message)
      this.clearCookies()
      return false
    }
  }

  // 清除 cookies
  clearCookies() {
    try {
      if (fs.existsSync(this.cookieFilePath)) {
        fs.unlinkSync(this.cookieFilePath)
        console.log('🗑️ 已清除过期的 Cookies')
      }
    } catch (error) {
      console.error('清除 Cookies 失败:', error.message)
    }
  }

  // 检查 cookies 是否存在且有效
  isValidCookies() {
    try {
      if (!fs.existsSync(this.cookieFilePath)) {
        return false
      }

      const cookieData = JSON.parse(fs.readFileSync(this.cookieFilePath))
      const sevenDays = 7 * 24 * 60 * 60 * 1000
      
      return Date.now() - cookieData.timestamp <= sevenDays
    } catch (error) {
      return false
    }
  }
}

module.exports = CookieManager
