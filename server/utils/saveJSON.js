// utils/saveJSON.js
const fs = require("fs")
const path = require("path")

async function saveJSON(savePath, movie) {
  try {
    const filePath = path.join(__dirname, "../", savePath) // 调整路径为相对根目录
    if (!fs.existsSync(filePath)) {
      await fs.promises.writeFile(filePath, "[]")
    }

    await fs.promises.mkdir(path.dirname(filePath), { recursive: true })
    console.log("Directory created successfully.")

    const data = await fs.promises.readFile(filePath, "utf-8")
    const list = data ? JSON.parse(data) : []

    list.push(movie)
    const uniqueMovies = list
      .sort((a, b) => new Date(b.done) - new Date(a.done))
      .reduce((acc, movie) => {
        acc[movie.title] = movie // 使用 title 去重
        return acc
      }, {})

    const uniqueMovieList = Object.values(uniqueMovies)
    await fs.promises.writeFile(
      filePath,
      JSON.stringify(uniqueMovieList, null, "\t")
    )
    console.log("JSON data is saved.")
  } catch (err) {
    console.error("Error saving JSON:", err)
    throw err // 抛出错误供调用方处理
  }
}

module.exports = saveJSON
