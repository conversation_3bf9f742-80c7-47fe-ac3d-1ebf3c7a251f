const fs = require("fs")
const readline = require("readline")

const TXT_PATH = "./movies.txt"
const JSON_PATH = "./data"

async function processLineByLine() {
  // ============= 读取 txt
  const fileStream = fs.createReadStream(TXT_PATH)

  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity,
  })
  const list = []
  let dir
  for await (let line of rl) {
    if (line) {
      line = line.trim()
      // 年份
      dir = line.substring(0, 4)
      list.push({
        title: line.substring(11, line.length),
        dir,
        done: line.substring(0, 10),
      })
    }
  }
  // console.log(dir, list, "一共===", list.length)
  // 观看电影的年份
  fs.writeFile(
    `${JSON_PATH}/${dir}.js`,
    JSON.stringify(list, "", "\t"),
    (err) => {
      if (err) {
        throw err
      }
    }
  )
  return list
}

module.exports = processLineByLine
