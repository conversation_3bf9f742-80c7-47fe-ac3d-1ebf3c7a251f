const CookieManager = require('./cookieManager')

class LoginManager {
  constructor(username = '豆友221876561的账号') {
    this.username = username
    this.cookieManager = new CookieManager()
  }

  // 检查登录状态
  async checkLoginStatus(page) {
    try {
      console.log('🔍 检查登录状态...')
      await page.goto('https://www.douban.com', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      })
      
      await page.waitForTimeout(3000)
      
      const isLoggedIn = await page.evaluate((username) => {
        const userElement = document.querySelector('.nav-user-account')
        const bodyText = document.body.innerText
        
        return (userElement && userElement.innerText.includes(username)) ||
               bodyText.includes(username)
      }, this.username)
      
      if (isLoggedIn) {
        console.log('✅ 已登录状态有效')
      } else {
        console.log('❌ 未登录或登录状态已失效')
      }
      
      return isLoggedIn
    } catch (error) {
      console.log('❌ 检查登录状态失败:', error.message)
      return false
    }
  }

  // 手动登录
  async manualLogin(page) {
    console.log('🔐 开始手动登录流程...')
    
    const loginUrl = "https://accounts.douban.com/passport/login"
    await page.goto(loginUrl, { waitUntil: "domcontentloaded" })

    console.log('📱 请在打开的页面中手动登录豆瓣账户...')
    console.log('💡 支持扫码登录或账号密码登录')

    // 等待用户登录成功
    await page.waitForFunction(
      (username) => {
        const userElement = document.querySelector(".nav-user-account") || document.body
        return userElement && userElement.innerText.includes(username)
      },
      { timeout: 0 },
      this.username
    )

    console.log('✅ 检测到登录成功！')
    
    // 保存登录状态
    await this.cookieManager.saveCookies(page)
    console.log('💾 登录状态已保存，下次将自动登录')
  }

  // 智能登录（主入口）
  async smartLogin(browser) {
    const loginPage = await browser.newPage()
    
    try {
      // 设置更真实的浏览器环境
      await loginPage.setViewport({ width: 1366, height: 768 })
      await loginPage.setUserAgent(
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      )

      // 尝试加载已保存的登录状态
      const cookiesLoaded = await this.cookieManager.loadCookies(loginPage)
      
      if (cookiesLoaded) {
        console.log('🔄 尝试使用已保存的登录状态...')
        const isLoggedIn = await this.checkLoginStatus(loginPage)
        
        if (isLoggedIn) {
          console.log('🎉 自动登录成功！无需手动操作')
          await loginPage.close()
          return
        } else {
          console.log('⚠️ 已保存的登录状态已失效')
          this.cookieManager.clearCookies()
        }
      }
      
      // 需要手动登录
      await this.manualLogin(loginPage)
      
    } catch (error) {
      console.error('❌ 登录过程出错:', error.message)
      throw error
    } finally {
      await loginPage.close()
    }
  }

  // 登出并清除保存的状态
  async logout() {
    this.cookieManager.clearCookies()
    console.log('👋 已登出并清除保存的登录状态')
  }

  // 检查是否有有效的保存状态
  hasValidSession() {
    return this.cookieManager.isValidCookies()
  }
}

module.exports = LoginManager
