# 电影信息爬虫 - 优化版

## 🚀 新功能特性

### 1. 智能登录系统
- **自动保存登录状态**: 首次登录后，系统会自动保存 Cookie
- **智能登录检测**: 下次运行时自动检查登录状态，无需重复登录
- **登录状态管理**: 提供登录状态检查和清除功能

### 2. 优化的用户体验
- **更友好的日志输出**: 使用 emoji 和颜色区分不同类型的信息
- **进度显示**: 显示当前处理进度 `[1/100]`
- **错误处理**: 更好的错误提示和处理机制

### 3. 模块化架构
- **分离关注点**: 登录、爬虫、配置分别管理
- **可配置**: 通过 `config.js` 统一管理配置
- **可扩展**: 易于添加新功能和修改现有功能

## 📖 使用方法

### 基本使用
```bash
# 运行爬虫（推荐）
npm run dev

# 或者
npm start
```

### 登录管理
```bash
# 检查当前登录状态
npm run check-login

# 清除保存的登录状态（强制下次重新登录）
npm run logout
```

## 🔄 工作流程

### 首次使用
1. 运行 `npm run dev`
2. 系统检测到没有登录状态，打开登录页面
3. 手动完成登录（扫码或账号密码）
4. 系统自动保存登录状态
5. 开始爬取电影信息

### 后续使用
1. 运行 `npm run dev`
2. 系统自动检查并使用保存的登录状态
3. 如果登录状态有效，直接开始爬取
4. 如果登录状态失效，提示重新登录

## ⚙️ 配置说明

### config.js 主要配置项
- `douban.username`: 豆瓣用户名
- `cookies.maxAge`: Cookie 有效期（默认7天）
- `scraper.delay.betweenMovies`: 电影之间的延迟时间
- `browser.headless`: 是否无头模式运行

## 🛠️ 故障排除

### 登录问题
```bash
# 如果登录出现问题，清除登录状态重试
npm run logout
npm run dev
```

### 检查登录状态
```bash
# 查看当前是否有有效的登录状态
npm run check-login
```

## 📁 文件结构
```
server/
├── index.js              # 主入口文件
├── movieScraper.js        # 电影爬虫类
├── config.js              # 配置文件
├── cookies.json           # 自动生成的登录状态文件
├── utils/
│   ├── loginManager.js    # 登录管理器
│   ├── cookieManager.js   # Cookie 管理器
│   └── ...               # 其他工具函数
└── scripts/
    ├── logout.js          # 登出脚本
    └── checkLogin.js      # 检查登录状态脚本
```

## 🎯 优化效果

- **减少手动操作**: 首次登录后，后续使用无需重复登录
- **提高效率**: 自动化程度更高，减少等待时间
- **更好的体验**: 清晰的进度提示和错误处理
- **易于维护**: 模块化设计，便于后续扩展和维护

## 🔒 安全说明

- `cookies.json` 文件包含登录信息，请勿分享或提交到版本控制
- Cookie 有效期设置为7天，过期后需要重新登录
- 建议定期清理登录状态以保证安全性
