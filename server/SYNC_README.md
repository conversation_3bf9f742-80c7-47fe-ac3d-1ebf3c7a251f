# 🔄 电影数据同步工具

## 📖 概述

这套工具可以在 JSON 数据和 Markdown 文件之间进行双向同步，保持数据的一致性。

## 🎯 解决的问题

- **数据不一致**: JSON 和 Markdown 文件内容不同步
- **手动维护**: 需要手动更新两种格式的数据
- **数据丢失**: 修改一边忘记更新另一边
- **冲突处理**: 两边都有更新时不知道如何合并

## 🚀 快速开始

### 1. 智能同步（推荐）
```bash
npm run sync:smart
```
自动检测哪个文件更新，然后同步到另一边。

### 2. 指定方向同步
```bash
# JSON → Markdown
npm run sync:j2m

# Markdown → JSON  
npm run sync:m2j
```

### 3. 数据对比
```bash
npm run sync:compare
```
比较两边数据的差异。

### 4. 自动监听
```bash
npm run watch
```
实时监听文件变化，自动同步。

## 📋 所有命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `npm run sync` | 显示帮助信息 | `npm run sync` |
| `npm run sync:smart` | 智能同步 | `npm run sync:smart` |
| `npm run sync:j2m` | JSON → Markdown | `npm run sync:j2m` |
| `npm run sync:m2j` | Markdown → JSON | `npm run sync:m2j` |
| `npm run sync:compare` | 数据对比 | `npm run sync:compare` |
| `npm run sync:merge` | 合并冲突 | `npm run sync:merge` |
| `npm run watch` | 自动监听 | `npm run watch` |
| `npm run watch:status` | 查看状态 | `npm run watch:status` |

## 🔧 工作原理

### JSON 格式
```json
[
  {
    "title": "肖申克的救赎",
    "imgName": "肖申克的救赎",
    "aka": "肖申克的救赎 The Shawshank Redemption",
    "rating": "9.7",
    "year": "1994",
    "done": "2024-07-31"
  }
]
```

### Markdown 格式
```markdown
2024-07-31 肖申克的救赎 The Shawshank Redemption‎ (1994)
```

### 同步规则
1. **智能检测**: 比较文件修改时间
2. **数据合并**: 以 `title + done` 为唯一键
3. **优先级**: JSON 数据优先（包含更多信息）
4. **备份保护**: 同步前自动备份原文件

## 📊 使用场景

### 场景1: 爬虫更新了 JSON
```bash
# 爬虫运行后，同步到 Markdown
npm run sync:j2m
```

### 场景2: 手动编辑了 Markdown
```bash
# 编辑 Markdown 后，同步到 JSON
npm run sync:m2j
```

### 场景3: 不确定哪个更新了
```bash
# 智能检测并同步
npm run sync:smart
```

### 场景4: 两边都有更新
```bash
# 先比较差异
npm run sync:compare

# 合并冲突
npm run sync:merge
```

### 场景5: 实时同步
```bash
# 启动监听服务
npm run watch
```

## 🛡️ 安全特性

### 自动备份
- 同步前自动备份原文件
- 备份文件名包含时间戳
- 可以随时恢复到之前的版本

### 数据验证
- 同步后自动验证数据完整性
- 统计电影数量对比
- 检测格式错误

### 冲突处理
- 智能合并重复数据
- 保留更完整的信息
- 避免数据丢失

## 🎯 最佳实践

### 1. 日常使用
```bash
# 每次修改数据后运行
npm run sync:smart
```

### 2. 开发调试
```bash
# 启动监听服务，实时同步
npm run watch
```

### 3. 数据检查
```bash
# 定期检查数据一致性
npm run sync:compare
```

### 4. 问题排查
```bash
# 查看详细状态
npm run watch:status

# 手动合并冲突
npm run sync:merge
```

## 🔍 故障排除

### 问题1: 同步失败
```bash
# 检查文件权限和路径
npm run sync:compare

# 查看详细错误信息
npm run sync:smart
```

### 问题2: 数据不一致
```bash
# 强制合并
npm run sync:merge

# 重新验证
npm run sync:compare
```

### 问题3: 文件损坏
```bash
# 查找备份文件
ls *.backup.*

# 恢复备份
cp 看过的电影.md.backup.1234567890 看过的电影.md
```

## 📈 高级功能

### 自定义同步规则
可以修改 `utils/bidirectionalSync.js` 来自定义同步逻辑。

### 批量处理
支持处理多年份的数据，自动创建目录结构。

### 格式转换
自动处理特殊字符、被封电影等特殊情况。

## 🎉 总结

这套同步工具让你可以：
- ✅ 自动保持数据一致性
- ✅ 安全地在两种格式间切换
- ✅ 实时监听文件变化
- ✅ 智能处理冲突
- ✅ 保护数据不丢失

推荐的工作流程：
1. 启动监听: `npm run watch`
2. 正常编辑文件
3. 系统自动同步
4. 定期检查: `npm run sync:compare`
