// main.js
const puppeteer = require("puppeteer")
const processLineByLine = require("./utils/read")
const saveJSON = require("./utils/saveJSON")
const downloadImg = require("./utils/downloadImg")
const sleep = require("./utils/sleep")

const launchConfig = {
  headless: false,
  slowMo: 25,
  defaultViewport: {
    width: 1600,
    height: 900,
  },
}

// 手动登录功能
async function manualLogin(browser) {
  const loginPage = await browser.newPage()
  const loginUrl = "https://accounts.douban.com/passport/login"

  console.log("请在打开的页面中手动登录豆瓣账户...")
  await loginPage.goto(loginUrl, { waitUntil: "domcontentloaded" })

  // 等待用户登录成功，检测右上角是否有 "豆友*********的账号"
  await loginPage.waitForFunction(
    () => {
      const userElement =
        document.querySelector(".nav-user-account") || document.body
      return (
        userElement && userElement.innerText.includes("豆友*********的账号")
      )
    },
    { timeout: 0 }
  )

  console.log("检测到登录成功（用户：豆友*********），继续执行后续操作...")
  await loginPage.close()
}

// 获取电影信息
async function getMovieInfo(page, selfDate, movieName) {
  const getDate = () => {
    const date = new Date()
    const nowMonth = String(date.getMonth() + 1).padStart(2, "0")
    const strDate = String(date.getDate()).padStart(2, "0")
    return `${date.getFullYear()}-${nowMonth}-${strDate}`
  }

  return await page.evaluate(
    (selfDate, movieName, getDate) => {
      try {
        const title = document.querySelector("h1 span:first-child").innerText
        const year = document
          .querySelector(".year")
          .innerText.replace(/[^0-9]/gi, "")
        const rating = document.querySelector(".rating_num").innerText
        const pattern =
          /[`~!@#$^&*()=|{}':;',\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g
        return {
          title,
          imgName: title.replace(pattern, ""),
          aka: title,
          rating,
          year,
          done: selfDate || getDate(),
        }
      } catch (error) {
        return {
          title: movieName,
          imgName: movieName,
          aka: movieName,
          rating: "被封了",
          year: selfDate?.substr(0, 4) || "未知",
          done: selfDate || getDate(),
        }
      }
    },
    selfDate,
    movieName,
    getDate
  )
}

// 获取图片链接
async function getImgLink(page) {
  return await page.evaluate(() => {
    const item = document.querySelector("#mainpic img")
    return item ? item.src : ""
  })
}

// 处理单个电影
async function processMovie(browser, params) {
  const MOVIE_NAME = params.title
  const SELF_DATE = params.done
  const IMG_SAVE_PATH = `../client/movies/${params.dir}/images`
  const JSON_SAVE_PATH = `../client/movies/${params.dir}/data/index.json`
  const url = `https://search.douban.com/movie/subject_search?search_text=${MOVIE_NAME}`

  console.log("MOVIE_NAME===========", MOVIE_NAME)

  const page = await browser.newPage()
  await page.setDefaultNavigationTimeout(0)

  try {
    await page.goto(url, { waitUntil: "domcontentloaded" })

    const subjectLink = await page.evaluate(() => {
      const subject = Array.from(document.querySelectorAll(".title a"))[0]
      return subject ? subject.href : ""
    })

    console.log("subjectLink=========", subjectLink)

    if (!subjectLink) {
      console.log("获取 subject 失败.........")
      return
    }

    await page.goto(subjectLink, { waitUntil: "domcontentloaded" })
    await page.waitForSelector("title")

    const movieInfo = await getMovieInfo(page, SELF_DATE, MOVIE_NAME)
    const imgLink = await getImgLink(page)

    await saveJSON(JSON_SAVE_PATH, movieInfo)
    if (imgLink) {
      await downloadImg(
        imgLink,
        `${IMG_SAVE_PATH}/${movieInfo.imgName}.jpg`,
        subjectLink
      )
    }
  } catch (err) {
    console.error(`处理 ${MOVIE_NAME} 时出错:`, err)
  } finally {
    await sleep(2000)
    await page.close()
  }
}

// 主函数
;(async () => {
  const list = await processLineByLine()
  const browser = await puppeteer.launch(launchConfig)

  try {
    await manualLogin(browser)

    for (const params of list) {
      await processMovie(browser, params)
      await sleep(5000) // 随机延迟可在此处添加
    }
  } catch (err) {
    console.error("主程序出错:", err)
  } finally {
    await browser.close()
  }
})()
