const puppeteer = require("puppeteer")
const processLineByLine = require("./utils/read")
const saveJSON = require("./utils/saveJSON")
const downloadImg = require("./utils/downloadImg")
const sleep = require("./utils/sleep")
const LoginManager = require("./utils/loginManager")
const config = require("./config")

class MovieScraper {
  constructor() {
    this.loginManager = new LoginManager(config.douban.username)
    this.browser = null
  }

  // 初始化浏览器
  async initBrowser() {
    console.log("🚀 启动浏览器...")
    this.browser = await puppeteer.launch(config.browser)
    console.log("✅ 浏览器启动成功")
  }

  // 关闭浏览器
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close()
      console.log("🔚 浏览器已关闭")
    }
  }

  // 获取电影信息
  async getMovieInfo(page, selfDate, movieName) {
    const getDate = () => {
      const date = new Date()
      const nowMonth = String(date.getMonth() + 1).padStart(2, "0")
      const strDate = String(date.getDate()).padStart(2, "0")
      return `${date.getFullYear()}-${nowMonth}-${strDate}`
    }

    return await page.evaluate(
      (selfDate, movieName, getDate) => {
        try {
          const title = document.querySelector("h1 span:first-child").innerText
          const year = document
            .querySelector(".year")
            .innerText.replace(/[^0-9]/gi, "")
          const rating = document.querySelector(".rating_num").innerText
          const pattern =
            /[`~!@#$^&*()=|{}':;',\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g
          return {
            title,
            imgName: title.replace(pattern, ""),
            aka: title,
            rating,
            year,
            done: selfDate || getDate(),
          }
        } catch (error) {
          return {
            title: movieName,
            imgName: movieName,
            aka: movieName,
            rating: "被封了",
            year: selfDate?.substr(0, 4) || "未知",
            done: selfDate || getDate(),
          }
        }
      },
      selfDate,
      movieName,
      getDate
    )
  }

  // 获取图片链接
  async getImgLink(page) {
    return await page.evaluate(() => {
      const item = document.querySelector("#mainpic img")
      return item ? item.src : ""
    })
  }

  // 处理单个电影
  async processMovie(params) {
    const MOVIE_NAME = params.title
    const SELF_DATE = params.done
    const IMG_SAVE_PATH = `${config.paths.clientMovies}/${params.dir}/${config.paths.images}`
    const JSON_SAVE_PATH = `${config.paths.clientMovies}/${params.dir}/${config.paths.data}`
    const url = `${config.douban.searchUrl}${MOVIE_NAME}`

    console.log(`🎬 处理电影: ${MOVIE_NAME}`)

    let page = null
    try {
      // 检查浏览器是否还在运行
      if (!this.browser || !this.browser.isConnected()) {
        throw new Error("浏览器连接已断开")
      }

      page = await this.browser.newPage()
      await page.setDefaultNavigationTimeout(config.scraper.timeout.navigation)

      // 设置页面环境
      await page.setUserAgent(config.browser.userAgent)

      await page.goto(url, { waitUntil: "domcontentloaded" })

      const subjectLink = await page.evaluate(() => {
        const subject = Array.from(document.querySelectorAll(".title a"))[0]
        return subject ? subject.href : ""
      })

      console.log(`🔗 电影链接: ${subjectLink}`)

      if (!subjectLink) {
        console.log(`❌ 获取 ${MOVIE_NAME} 的链接失败`)
        return
      }

      await page.goto(subjectLink, { waitUntil: "domcontentloaded" })
      await page.waitForSelector("title", {
        timeout: config.scraper.timeout.waitForElement,
      })

      const movieInfo = await this.getMovieInfo(page, SELF_DATE, MOVIE_NAME)
      const imgLink = await this.getImgLink(page)

      await saveJSON(JSON_SAVE_PATH, movieInfo)

      if (imgLink) {
        await downloadImg(
          imgLink,
          `${IMG_SAVE_PATH}/${movieInfo.imgName}.jpg`,
          subjectLink
        )
      }

      console.log(`✅ ${MOVIE_NAME} 处理完成`)
    } catch (err) {
      console.error(`❌ 处理 ${MOVIE_NAME} 时出错:`, err.message)

      // 如果是连接错误，重新抛出以便上层处理
      if (
        err.message.includes("Connection closed") ||
        err.message.includes("浏览器连接已断开")
      ) {
        throw err
      }
    } finally {
      if (page && !page.isClosed()) {
        try {
          await sleep(config.scraper.delay.afterRequest)
          await page.close()
        } catch (closeErr) {
          console.log("⚠️ 关闭页面时出错:", closeErr.message)
        }
      }
    }
  }

  // 主执行函数
  async run() {
    try {
      console.log("🎭 电影信息爬虫启动")
      console.log("=".repeat(50))

      // 检查是否有有效的登录状态
      if (this.loginManager.hasValidSession()) {
        console.log("💾 发现有效的登录状态")
      } else {
        console.log("🔐 需要重新登录")
      }

      // 初始化浏览器
      await this.initBrowser()

      // 智能登录
      await this.loginManager.smartLogin(this.browser)

      // 读取电影列表
      console.log("📖 读取电影列表...")
      const movieList = await processLineByLine()
      console.log(`📊 共找到 ${movieList.length} 部电影待处理`)

      if (movieList.length === 0) {
        console.log("⚠️ 没有找到待处理的电影")
        return
      }

      // 处理每部电影
      for (let i = 0; i < movieList.length; i++) {
        const movie = movieList[i]
        console.log(`\n[${i + 1}/${movieList.length}] 开始处理...`)

        try {
          await this.processMovie(movie)
        } catch (movieErr) {
          console.error(
            `❌ 处理电影 ${movie.title} 时发生严重错误:`,
            movieErr.message
          )

          // 如果是浏览器连接问题，尝试重新初始化
          if (
            movieErr.message.includes("Connection closed") ||
            movieErr.message.includes("浏览器连接已断开")
          ) {
            console.log("🔄 尝试重新初始化浏览器...")
            try {
              await this.closeBrowser()
              await this.initBrowser()
              await this.loginManager.smartLogin(this.browser)
              console.log("✅ 浏览器重新初始化成功，继续处理...")
            } catch (reinitErr) {
              console.error("❌ 重新初始化浏览器失败:", reinitErr.message)
              throw reinitErr
            }
          }
        }

        // 电影之间的延迟
        if (i < movieList.length - 1) {
          console.log(
            `⏳ 等待 ${config.scraper.delay.betweenMovies / 1000} 秒...`
          )
          await sleep(config.scraper.delay.betweenMovies)
        }
      }

      console.log("\n🎉 所有电影处理完成！")
    } catch (err) {
      console.error("❌ 程序执行出错:", err.message)
      console.error("💡 如果问题持续，请尝试运行: npm run logout 然后重新运行")
      throw err
    } finally {
      await this.closeBrowser()
    }
  }

  // 清除登录状态
  async logout() {
    await this.loginManager.logout()
  }
}

module.exports = MovieScraper
