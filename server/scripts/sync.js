#!/usr/bin/env node

const BidirectionalSyncer = require('../utils/bidirectionalSync')

// 命令行参数处理
const args = process.argv.slice(2)
const command = args[0] || 'help'

const syncer = new BidirectionalSyncer()

async function main() {
  console.log('🔄 电影数据同步工具')
  console.log('=' .repeat(50))

  switch (command) {
    case 'json-to-md':
    case 'j2m':
      console.log('📊 JSON → Markdown')
      await syncer.syncToMarkdown()
      await syncer.validateSync()
      break

    case 'md-to-json':
    case 'm2j':
      console.log('📝 Markdown → JSON')
      await syncer.syncFromMarkdown()
      break

    case 'smart':
    case 'auto':
      console.log('🧠 智能同步')
      await syncer.smartSync()
      break

    case 'merge':
      console.log('🔀 合并冲突')
      await syncer.mergeConflicts()
      break

    case 'compare':
    case 'diff':
      console.log('📊 数据对比')
      await syncer.compareData()
      break

    case 'validate':
    case 'check':
      console.log('🔍 验证数据')
      await syncer.validateSync()
      break

    case 'backup':
      console.log('💾 创建备份')
      await createBackup()
      break

    case 'help':
    case '-h':
    case '--help':
    default:
      showHelp()
      break
  }
}

function showHelp() {
  console.log(`
📖 使用方法:
  node scripts/sync.js <command>

🔧 可用命令:
  json-to-md, j2m    将 JSON 数据同步到 Markdown
  md-to-json, m2j    将 Markdown 数据同步到 JSON
  smart, auto        智能同步（自动检测哪个文件更新）
  merge              合并冲突（当两边都有更新时）
  compare, diff      比较 JSON 和 Markdown 的数据差异
  validate, check    验证同步结果
  backup             创建数据备份
  help, -h, --help   显示帮助信息

💡 示例:
  npm run sync smart          # 智能同步
  npm run sync json-to-md     # JSON 同步到 Markdown
  npm run sync compare        # 比较数据差异

🎯 推荐工作流:
  1. 修改数据后运行: npm run sync smart
  2. 如有冲突运行: npm run sync merge
  3. 验证结果: npm run sync validate
`)
}

async function createBackup() {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupDir = `backup-${timestamp}`
    
    const fs = require('fs')
    const path = require('path')
    
    // 创建备份目录
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true })
    }
    
    // 备份 Markdown 文件
    const markdownPath = path.join(__dirname, '../../看过的电影.md')
    if (fs.existsSync(markdownPath)) {
      fs.copyFileSync(markdownPath, path.join(backupDir, '看过的电影.md'))
    }
    
    // 备份 JSON 文件
    const moviesDir = path.join(__dirname, '../../client/movies')
    if (fs.existsSync(moviesDir)) {
      const copyDir = (src, dest) => {
        if (!fs.existsSync(dest)) {
          fs.mkdirSync(dest, { recursive: true })
        }
        
        const items = fs.readdirSync(src)
        for (const item of items) {
          const srcPath = path.join(src, item)
          const destPath = path.join(dest, item)
          
          if (fs.statSync(srcPath).isDirectory()) {
            copyDir(srcPath, destPath)
          } else {
            fs.copyFileSync(srcPath, destPath)
          }
        }
      }
      
      copyDir(moviesDir, path.join(backupDir, 'movies'))
    }
    
    console.log(`✅ 备份创建成功: ${backupDir}`)
    
  } catch (error) {
    console.error('❌ 创建备份失败:', error.message)
  }
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的错误:', error.message)
  process.exit(1)
})

// 运行主函数
main().catch(error => {
  console.error('❌ 程序执行失败:', error.message)
  process.exit(1)
})
