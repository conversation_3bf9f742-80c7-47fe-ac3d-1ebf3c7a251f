const fs = require('fs')
const path = require('path')
const BidirectionalSyncer = require('../utils/bidirectionalSync')

class FileWatcher {
  constructor() {
    this.syncer = new BidirectionalSyncer()
    this.markdownPath = path.join(__dirname, '../../看过的电影.md')
    this.moviesDir = path.join(__dirname, '../../client/movies')
    this.isProcessing = false
    this.debounceTimer = null
  }

  // 防抖处理
  debounce(func, delay = 2000) {
    clearTimeout(this.debounceTimer)
    this.debounceTimer = setTimeout(func, delay)
  }

  // 处理文件变化
  async handleFileChange(filePath, changeType) {
    if (this.isProcessing) {
      console.log('⏳ 正在处理中，跳过此次变化...')
      return
    }

    this.isProcessing = true

    try {
      console.log(`\n📁 文件变化: ${path.basename(filePath)} (${changeType})`)
      console.log('🔄 开始自动同步...')

      const success = await this.syncer.smartSync()
      
      if (success) {
        console.log('✅ 自动同步完成!')
        
        // 显示统计信息
        await this.syncer.compareData()
      } else {
        console.log('❌ 自动同步失败')
      }

    } catch (error) {
      console.error('❌ 处理文件变化失败:', error.message)
    } finally {
      this.isProcessing = false
    }
  }

  // 监听 Markdown 文件
  watchMarkdown() {
    if (!fs.existsSync(this.markdownPath)) {
      console.log('⚠️ Markdown 文件不存在，跳过监听')
      return
    }

    console.log('👀 开始监听 Markdown 文件...')
    
    fs.watchFile(this.markdownPath, { interval: 1000 }, (curr, prev) => {
      if (curr.mtime !== prev.mtime) {
        this.debounce(() => {
          this.handleFileChange(this.markdownPath, 'modified')
        })
      }
    })
  }

  // 监听 JSON 文件
  watchJsonFiles() {
    if (!fs.existsSync(this.moviesDir)) {
      console.log('⚠️ Movies 目录不存在，跳过监听')
      return
    }

    console.log('👀 开始监听 JSON 文件...')

    // 递归监听目录
    const watchDirectory = (dir) => {
      try {
        const watcher = fs.watch(dir, { recursive: true }, (eventType, filename) => {
          if (filename && filename.endsWith('.json') && filename.includes('index.json')) {
            const fullPath = path.join(dir, filename)
            
            this.debounce(() => {
              this.handleFileChange(fullPath, eventType)
            })
          }
        })

        watcher.on('error', (error) => {
          console.error('❌ 监听目录出错:', error.message)
        })

        return watcher
      } catch (error) {
        console.error('❌ 无法监听目录:', error.message)
        return null
      }
    }

    return watchDirectory(this.moviesDir)
  }

  // 开始监听
  start() {
    console.log('🚀 启动文件监听服务')
    console.log('=' .repeat(50))
    console.log('📁 监听目录:')
    console.log(`   - Markdown: ${this.markdownPath}`)
    console.log(`   - JSON: ${this.moviesDir}`)
    console.log('💡 提示: 按 Ctrl+C 停止监听\n')

    // 启动监听
    this.watchMarkdown()
    const jsonWatcher = this.watchJsonFiles()

    // 优雅退出
    process.on('SIGINT', () => {
      console.log('\n🛑 停止文件监听...')
      
      // 清理资源
      fs.unwatchFile(this.markdownPath)
      if (jsonWatcher) {
        jsonWatcher.close()
      }
      
      console.log('👋 监听服务已停止')
      process.exit(0)
    })

    // 初始同步
    console.log('🔄 执行初始同步...')
    this.syncer.smartSync().then(() => {
      console.log('✅ 初始同步完成，开始监听文件变化...\n')
    }).catch(error => {
      console.error('❌ 初始同步失败:', error.message)
    })
  }

  // 显示状态
  async showStatus() {
    console.log('📊 当前状态:')
    
    try {
      const comparison = await this.syncer.compareData()
      if (comparison) {
        console.log(`   - JSON 电影数: ${comparison.jsonCount}`)
        console.log(`   - Markdown 电影数: ${comparison.markdownCount}`)
        console.log(`   - 数据差异: ${comparison.difference}`)
        
        if (comparison.difference === 0) {
          console.log('   ✅ 数据已同步')
        } else {
          console.log('   ⚠️ 数据不同步')
        }
      }
    } catch (error) {
      console.log('   ❌ 无法获取状态')
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const watcher = new FileWatcher()
  
  // 检查命令行参数
  const args = process.argv.slice(2)
  const command = args[0]

  if (command === 'status') {
    watcher.showStatus()
  } else {
    watcher.start()
  }
}

module.exports = FileWatcher
