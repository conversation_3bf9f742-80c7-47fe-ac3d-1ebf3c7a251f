// 调试脚本 - 帮助诊断问题
const LoginManager = require('../utils/loginManager')
const processLineByLine = require('../utils/read')
const puppeteer = require('puppeteer')
const config = require('../config')

async function debugInfo() {
  console.log('🔍 系统诊断信息')
  console.log('=' .repeat(50))
  
  // 1. 检查登录状态
  console.log('1️⃣ 检查登录状态...')
  const loginManager = new LoginManager()
  if (loginManager.hasValidSession()) {
    console.log('✅ 发现有效的登录状态')
  } else {
    console.log('❌ 没有有效的登录状态')
  }
  
  // 2. 检查电影列表
  console.log('\n2️⃣ 检查电影列表...')
  try {
    const movieList = await processLineByLine()
    console.log(`✅ 成功读取 ${movieList.length} 部电影`)
    if (movieList.length > 0) {
      console.log('📋 前3部电影:')
      movieList.slice(0, 3).forEach((movie, index) => {
        console.log(`   ${index + 1}. ${movie.title} (${movie.done})`)
      })
    }
  } catch (err) {
    console.log('❌ 读取电影列表失败:', err.message)
  }
  
  // 3. 测试浏览器启动
  console.log('\n3️⃣ 测试浏览器启动...')
  let browser = null
  try {
    browser = await puppeteer.launch(config.browser)
    console.log('✅ 浏览器启动成功')
    
    const page = await browser.newPage()
    console.log('✅ 创建新页面成功')
    
    await page.goto('https://www.douban.com', { waitUntil: 'domcontentloaded', timeout: 10000 })
    console.log('✅ 访问豆瓣首页成功')
    
    await page.close()
  } catch (err) {
    console.log('❌ 浏览器测试失败:', err.message)
  } finally {
    if (browser) {
      await browser.close()
      console.log('✅ 浏览器已关闭')
    }
  }
  
  // 4. 检查配置
  console.log('\n4️⃣ 检查配置信息...')
  console.log(`📁 电影数据路径: ${config.paths.clientMovies}`)
  console.log(`⏱️ 电影间延迟: ${config.scraper.delay.betweenMovies}ms`)
  console.log(`🕐 导航超时: ${config.scraper.timeout.navigation}ms`)
  console.log(`👤 用户名: ${config.douban.username}`)
  
  console.log('\n🎯 诊断完成！')
}

// 运行诊断
debugInfo().catch(err => {
  console.error('❌ 诊断过程出错:', err.message)
  process.exit(1)
})
