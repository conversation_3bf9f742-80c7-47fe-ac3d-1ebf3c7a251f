{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "node scripts/test.js", "dev": "node index.js", "start": "node index.js", "logout": "node scripts/logout.js", "check-login": "node scripts/checkLogin.js", "debug": "node scripts/debug.js", "sync": "node scripts/sync.js", "sync:smart": "node scripts/sync.js smart", "sync:j2m": "node scripts/sync.js json-to-md", "sync:m2j": "node scripts/sync.js md-to-json", "sync:compare": "node scripts/sync.js compare", "sync:merge": "node scripts/sync.js merge", "watch": "node scripts/watch.js", "watch:status": "node scripts/watch.js status"}, "author": "", "license": "ISC", "dependencies": {"puppeteer": "^13.0.1"}, "pnpm": {"onlyBuiltDependencies": ["puppeteer"]}}