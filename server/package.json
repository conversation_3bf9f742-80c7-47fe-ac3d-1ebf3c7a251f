{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "node scripts/test.js", "dev": "node index.js", "start": "node index.js", "logout": "node scripts/logout.js", "check-login": "node scripts/checkLogin.js", "debug": "node scripts/debug.js"}, "author": "", "license": "ISC", "dependencies": {"puppeteer": "^13.0.1"}, "pnpm": {"onlyBuiltDependencies": ["puppeteer"]}}