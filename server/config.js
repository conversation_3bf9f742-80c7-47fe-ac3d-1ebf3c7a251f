module.exports = {
  // 豆瓣用户配置
  douban: {
    username: '豆友*********的账号',
    loginUrl: 'https://accounts.douban.com/passport/login',
    homeUrl: 'https://www.douban.com',
    searchUrl: 'https://search.douban.com/movie/subject_search?search_text='
  },

  // 浏览器配置
  browser: {
    headless: false,
    slowMo: 25,
    defaultViewport: {
      width: 1600,
      height: 900,
    },
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  },

  // Cookie 配置
  cookies: {
    filePath: './cookies.json',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
  },

  // 爬虫配置
  scraper: {
    delay: {
      betweenMovies: 5000,    // 电影之间的延迟
      afterRequest: 2000,     // 请求后的延迟
      loginCheck: 3000,       // 登录检查延迟
    },
    timeout: {
      navigation: 30000,      // 页面导航超时
      waitForElement: 10000,  // 等待元素超时
    },
    retry: {
      maxAttempts: 3,         // 最大重试次数
      delay: 1000,            // 重试延迟
    }
  },

  // 路径配置
  paths: {
    movieList: '../看过的电影.md',
    clientMovies: '../client/movies',
    images: 'images',
    data: 'data/index.json'
  }
}
